quarkus.datasource.db-kind=postgresql
quarkus.datasource.username=${POSTGRES_DB_USERNAME}
quarkus.datasource.password=${POSTGRES_DB_PASSWORD}
quarkus.datasource.jdbc.url=jdbc:postgresql://${POSTGRES_DB_HOST}/${POSTGRES_DB_DATABASE}

# Hibernate Configuration
quarkus.hibernate-orm.database.generation=update
quarkus.hibernate-orm.sql-load-script=no-file
quarkus.hibernate-orm.physical-naming-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
quarkus.hibernate-orm.log.sql=false
quarkus.hibernate-orm.log.bind-parameters=false

# Connection Pool Optimization (using correct property names)
quarkus.datasource.jdbc.initial-size=5
quarkus.datasource.jdbc.min-size=5
quarkus.datasource.jdbc.max-size=20
quarkus.datasource.jdbc.acquisition-timeout=30S

# HTTP Configuration
quarkus.http.host=0.0.0.0
quarkus.http.port=8080

# API Path Configuration (Fixed for Quarkus 3.x)
#quarkus.rest.path=/api
quarkus.http.root-path=/api

# CORS Configuration
quarkus.http.cors=true
quarkus.http.cors.origins=*
quarkus.http.cors.methods=GET,POST,PUT,DELETE,OPTIONS,PATCH
quarkus.http.cors.headers=Content-Type,Authorization,X-Requested-With,Accept,Origin
quarkus.http.cors.exposed-headers=Content-Disposition
quarkus.http.cors.access-control-max-age=24H

# OpenAPI/Swagger Configuration
quarkus.smallrye-openapi.path=/q/openapi
quarkus.swagger-ui.always-include=true
quarkus.swagger-ui.path=/q/swagger-ui
quarkus.swagger-ui.title=EHR Backend API
quarkus.swagger-ui.theme=outline

# Security Configuration
quarkus.oidc.enabled=false
# JWT Configuration (when enabled)
# smallrye.jwt.sign.key.location=classpath:privateKey.pem

# Flyway Migration
quarkus.flyway.migrate-at-start=false
quarkus.flyway.baseline-on-migrate=false
quarkus.flyway.locations=classpath:db/migration
quarkus.flyway.validate-on-migrate=false

# Logging Configuration
quarkus.log.level=INFO
quarkus.log.category."sirobilt.meghasanjivini".level=INFO
quarkus.log.category."org.hibernate.SQL".level=WARN
quarkus.log.category."org.hibernate.type.descriptor.sql.BasicBinder".level=WARN
quarkus.log.console.format=%d{HH:mm:ss} %-5p [%c{2.}] (%t) %s%e%n

# Performance Configuration
quarkus.live-reload.enabled=true
quarkus.hibernate-orm.fetch.batch-size=16
quarkus.hibernate-orm.jdbc.statement-batch-size=25

# Cache Configuration
quarkus.cache.caffeine."lookup-cache".expire-after-write=1H
quarkus.cache.caffeine."lookup-cache".maximum-size=1000
quarkus.cache.caffeine."facility-cache".expire-after-write=30M
quarkus.cache.caffeine."facility-cache".maximum-size=500

# Health Check Configuration
quarkus.smallrye-health.root-path=/q/health

# Metrics Configuration
quarkus.smallrye-metrics.path=/q/metrics

# JSON Configuration
quarkus.jackson.fail-on-unknown-properties=false
quarkus.jackson.write-dates-as-timestamps=false
quarkus.jackson.serialization-inclusion=non_null

# Duplicate Detection Configuration
duplicate.detection.enabled=true
duplicate.detection.threshold.high=85
duplicate.detection.threshold.medium=70
duplicate.detection.timeout.seconds=2
duplicate.detection.audit.enabled=true
duplicate.detection.batch.enabled=true

# --- Disable Dev Services ---
quarkus.kafka.devservices.enabled=false



# --- Reactive Messaging channels ---
mp.messaging.outgoing.audit-emitter.connector=smallrye-kafka
mp.messaging.outgoing.audit-emitter.topic=audit-events
mp.messaging.outgoing.audit-emitter.value.serializer=io.quarkus.kafka.client.serialization.ObjectMapperSerializer
mp.messaging.outgoing.audit-emitter.bootstrap.servers=localhost:29092

mp.messaging.incoming.audit-listener.connector=smallrye-kafka
mp.messaging.incoming.audit-listener.topic=audit-events
mp.messaging.incoming.audit-listener.value.deserializer=sirobilt.meghasanjivini.auditstrail.AuditEntryDeserializer
mp.messaging.incoming.audit-listener.bootstrap.servers=localhost:29092
mp.messaging.incoming.audit-listener.group.id=ehr-audit-consumers

quarkus.jackson.serialization.fail-on-self-references=true







