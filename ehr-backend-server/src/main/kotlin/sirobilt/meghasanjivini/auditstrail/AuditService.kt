package sirobilt.meghasanjivini.auditstrail

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import org.eclipse.microprofile.reactive.messaging.Channel
import org.eclipse.microprofile.reactive.messaging.Emitter

@ApplicationScoped
class AuditService {

    @Inject
    @Channel("audit-emitter") // Refers to outgoing config in application.properties
    lateinit var emitter: Emitter<AuditEntry>

    /**
     * Asynchronously sends an audit entry to Kafka.
     */
    fun logAsync(entry: AuditEntry) {

        println("Sending audit entry to Kafka: $entry")
        emitter.send(entry) // Non-blocking, fire-and-forget
    }
}
