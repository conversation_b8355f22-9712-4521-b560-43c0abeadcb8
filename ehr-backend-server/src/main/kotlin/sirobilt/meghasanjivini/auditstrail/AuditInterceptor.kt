package sirobilt.meghasanjivini.auditstrail

import jakarta.annotation.Priority
import jakarta.inject.Inject
import jakarta.interceptor.AroundInvoke
import jakarta.interceptor.Interceptor
import jakarta.interceptor.InvocationContext
import org.slf4j.LoggerFactory
import java.time.Instant
import java.util.*

@Interceptor
@Auditable
@Priority(Interceptor.Priority.APPLICATION)
class AuditInterceptor {

    @Inject
    lateinit var auditService: AuditService

    private val logger = LoggerFactory.getLogger(AuditInterceptor::class.java)

    @AroundInvoke
    fun audit(ctx: InvocationContext): Any? {

        println(">>> AuditInterceptor called for: ${ctx.method.name}")
        val method = ctx.method
        val annotation = method.getAnnotation(AuditEvent::class.java) ?: return ctx.proceed()

        // Example: extract user, ip, etc. Replace with actual context retrieval logic!
        val userId = getCurrentUserId() // TODO: Integrate with your security context
        val ipAddress = getClientIp()   // TODO: Integrate with your request context
        val traceId = getCurrentTraceId() // TODO: Integrate with your tracing/logging context

        // Optionally, extract some DTO params as metadata
        val metadata = extractMetadata(ctx.parameters)

        val entry = AuditEntry(
            userId = userId,
            action = annotation.action,
            resource = annotation.resource,
            timestamp = Instant.now().toString(),
            ipAddress = ipAddress,
            traceId = traceId,
            level = annotation.level.name,
            metadata = metadata
        )

        logger.info("AUDIT: $entry")
        auditService.logAsync(entry)

        return ctx.proceed()
    }

    // --- Replace these methods with your own security/context retrieval ---

    private fun getCurrentUserId(): String =
        try {
            // Example: use SecurityContext, JWT, etc.
            // Return a placeholder if not available
            "system-user"
        } catch (e: Exception) {
            "unknown"
        }

    private fun getClientIp(): String =
        try {
            // Example: use thread local, MDC, or injected request context
            "127.0.0.1"
        } catch (e: Exception) {
            "unknown"
        }

    private fun getCurrentTraceId(): String =
        try {
            // Example: use MDC (SLF4J), or OpenTelemetry context
            UUID.randomUUID().toString()
        } catch (e: Exception) {
            "unknown"
        }

    /**
     * Extract fields of interest from parameters for metadata.
     */
    private fun extractMetadata(params: Array<Any?>): Map<String, Any> =
        params.filterNotNull().flatMap { param ->
            param::class.members
                .filter { it.parameters.size == 1 } // Only no-arg getters
                .mapNotNull { member ->
                    try {
                        val value = member.call(param)
                        if (value != null) member.name to value else null
                    } catch (_: Exception) {
                        null
                    }
                }
        }.toMap()
}
