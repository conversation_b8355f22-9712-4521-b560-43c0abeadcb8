package sirobilt.meghasanjivini.auditstrail

import io.smallrye.reactive.messaging.annotations.Blocking
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import org.eclipse.microprofile.reactive.messaging.Incoming
import sirobilt.meghasanjivini.audit.AuditRepository

@ApplicationScoped
class AuditListener {

    @Inject
    lateinit var auditRepository: AuditRepository

    /**
     * Receives audit entries from Kafka and persists to database.
     */
    @Incoming("audit-listener") // Refers to incoming config in application.properties
    @Blocking // Ensures DB writes don’t block event loop
    fun receive(entry: AuditEntry?) {
        if (entry == null) {
            println("AuditListener received NULL entry! Check deserializer.")
            return
        }
        auditRepository.persist(entry)
    }
}
