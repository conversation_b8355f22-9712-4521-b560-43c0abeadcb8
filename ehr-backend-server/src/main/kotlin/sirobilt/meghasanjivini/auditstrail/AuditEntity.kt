package sirobilt.meghasanjivini.auditstrail

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import jakarta.persistence.*
import java.time.Instant
import java.util.*



data class AuditEntry(
    val userId: String,
    val action: String,
    val resource: String,
    val timestamp: String =  Instant.now().toString(),
    val ipAddress: String,
    val traceId: String,
    val level: String,
    val metadata: Map<String, Any> = emptyMap()
)


@Entity
@Table(name = "audit_logs")
open class AuditEntity(
    @Id
    @GeneratedValue
    @Column(columnDefinition = "uuid", updatable = false, nullable = false)
    var id: UUID? = null,
    open var userId: String = "",
    open var action: String = "",
    open var resource: String = "",
    open var timestamp: String = Instant.now().toString(),
    open var ipAddress: String = "",
    open var traceId: String = "",
    open var level: String = "",
    @Lob
    open var metadata: String = ""
) {
    // Default constructor required by JPA
    constructor() : this(
        id = null,
        userId = "",
        action = "",
        resource = "",
        timestamp = Instant.now().toString(),
        ipAddress = "",
        traceId = "",
        level = "",
        metadata = ""
    )

    companion object {
        private val mapper = jacksonObjectMapper()
        fun from(entry: AuditEntry): AuditEntity = AuditEntity(
            userId = entry.userId,
            action = entry.action,
            resource = entry.resource,
            timestamp = entry.timestamp,
            ipAddress = entry.ipAddress,
            traceId = entry.traceId,
            level = entry.level,
            metadata = mapper.writeValueAsString(entry.metadata)
        )
    }
}