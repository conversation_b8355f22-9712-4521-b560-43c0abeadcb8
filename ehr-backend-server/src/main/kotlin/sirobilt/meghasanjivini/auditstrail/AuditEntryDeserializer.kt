package sirobilt.meghasanjivini.auditstrail

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.apache.kafka.common.serialization.Deserializer

class AuditEntryDeserializer : Deserializer<AuditEntry> {
    private val mapper = jacksonObjectMapper()
    override fun deserialize(topic: String, data: ByteArray?): AuditEntry? {
        println(">>> [DESERIALIZER] Called with data: ${data?.decodeToString()}")
        return try {
            data?.let {
                val result = mapper.readValue(it, AuditEntry::class.java)
                println(">>> [DESERIALIZER] Parsed: $result")
                result
            }
        } catch (e: Exception) {
            println(">>> [DESERIALIZER] EXCEPTION: ${e.message}")
            null
        }
    }
}