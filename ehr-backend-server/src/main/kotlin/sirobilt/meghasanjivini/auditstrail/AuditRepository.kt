package sirobilt.meghasanjivini.audit

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.persistence.EntityManager
import jakarta.transaction.Transactional
import sirobilt.meghasanjivini.auditstrail.AuditEntity
import sirobilt.meghasanjivini.auditstrail.AuditEntry

@ApplicationScoped
class AuditRepository @Inject constructor(
    private val entityManager: EntityManager
) {
    @Transactional
    fun persist(entry: AuditEntry) {
        println("Persisting audit entry to DB: $entry")
        entityManager.persist(AuditEntity.from(entry))
    }
}