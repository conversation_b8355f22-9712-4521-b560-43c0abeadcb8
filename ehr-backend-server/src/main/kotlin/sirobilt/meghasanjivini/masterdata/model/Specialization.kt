// src/main/kotlin/com/sirobilt/domain/Specialization.kt
package com.sirobilt.domain

import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.*
import java.util.*


@Entity
@Table(name = "specializations")
class Specialization : PanacheEntityBase {

    @Id
    @Column(name = "specialization_id", columnDefinition = "UUID")
    var specializationId: UUID? = null

    @Column(name = "name", nullable = false, length = 100)
    lateinit var name: String

    @Column(name = "code", nullable = false, length = 50, unique = true)
    lateinit var code: String

    @Column(name = "is_clinical", nullable = false)
    var isClinical: Boolean = false

    @Column(name = "description", length = 255)
    var description: String? = null


}
