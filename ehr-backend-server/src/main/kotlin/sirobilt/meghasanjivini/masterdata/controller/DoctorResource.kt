package sirobilt.meghasanjivini.masterdata.controller

import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.ws.rs.*
import jakarta.ws.rs.core.*
import sirobilt.meghasanjivini.department.model.Department
import sirobilt.meghasanjivini.department.repository.DepartmentRepository
import sirobilt.meghasanjivini.masterdata.dto.DoctorDto
import sirobilt.meghasanjivini.masterdata.dto.ListDoctorDto
import sirobilt.meghasanjivini.masterdata.model.Address
import sirobilt.meghasanjivini.masterdata.model.Doctor
import sirobilt.meghasanjivini.masterdata.repository.DoctorRepository
import java.net.URI
import java.time.LocalDateTime
import java.util.*

@Path("/doctors")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
class DoctorResource {

    @Inject
    lateinit var doctorRepo: DoctorRepository

    @Inject
    lateinit var departmentRepo: DepartmentRepository

    @GET
    fun listAll(): Response {
        val list = doctorRepo.listAll().map { ListDoctorDto.fromEntity(it) }
        return Response.ok(list).build()
    }

    @GET
    @Path("active")
    fun listActive(): Response {
        val active = doctorRepo.findAllActive().map { DoctorDto.fromEntity(it) }
        return Response.ok(active).build()
    }

    @GET
    @Path("{id}")
    fun getById(@PathParam("id") id: UUID): Response {
        val doc = doctorRepo.findById(id) ?: return Response.status(Response.Status.NOT_FOUND).build()
        return Response.ok(DoctorDto.fromEntity(doc)).build()
    }

    @GET
    @Path("specialization/{specId}")
    fun listBySpecialization(@PathParam("specId") specId: String): Response {
        val spec: Department = departmentRepo.findById(specId)
            ?: return Response.status(Response.Status.NOT_FOUND).entity("Specialization not found").build()

        val docs = doctorRepo.listBySpecialization(specId).map { DoctorDto.fromEntity(it) }
        return Response.ok(docs).build()
    }

    @POST
    @Transactional
    fun create(dto: DoctorDto, @Context uriInfo: UriInfo): Response {

        val doctor = Doctor().apply {
            doctorId = UUID.randomUUID()
            firstName = dto.firstName
            lastName = dto.lastName
            middleName = dto.middleName
            specialization = dto.specialization
            roleType = dto.roleType
            qualification = dto.qualification
            gender = dto.gender
            dateOfBirth = dto.dateOfBirth
            mobileNumber = dto.mobileNumber
            email = dto.email
            registrationNumber = dto.registrationNumber
            registrationState = dto.registrationState
            yearsOfExperience = dto.yearsOfExperience
            telemedicineReady = dto.telemedicineReady
            languagesSpoken = dto.languagesSpoken
            isActive = dto.isActive
            address = Address().apply {
                street = dto.address.street
                city = dto.address.city
                state = dto.address.state
                zipCode = dto.address.zipCode
                country = dto.address.country
            }
            createdAt = dto.createdAt ?: LocalDateTime.now()
        }

        doctorRepo.persist(doctor)
        val uri: URI = uriInfo.absolutePathBuilder.path(doctor.doctorId.toString()).build()
        return Response.created(uri).entity(DoctorDto.fromEntity(doctor)).build()
    }

    @PUT
    @Path("{id}")
    @Transactional
    fun update(@PathParam("id") id: UUID, updatedDto: DoctorDto): Response {
        val existing = doctorRepo.findById(id)
            ?: return Response.status(Response.Status.NOT_FOUND).build()



        existing.firstName = updatedDto.firstName
        existing.lastName = updatedDto.lastName
        existing.middleName = updatedDto.middleName
        existing.roleType = updatedDto.roleType
        existing.qualification = updatedDto.qualification
        existing.specialization = updatedDto.specialization
        existing.gender = updatedDto.gender
        existing.dateOfBirth = updatedDto.dateOfBirth
        existing.mobileNumber = updatedDto.mobileNumber
        existing.email = updatedDto.email
        existing.registrationNumber = updatedDto.registrationNumber
        existing.registrationState = updatedDto.registrationState
        existing.yearsOfExperience = updatedDto.yearsOfExperience
        existing.telemedicineReady = updatedDto.telemedicineReady
        existing.languagesSpoken = updatedDto.languagesSpoken
        existing.isActive = updatedDto.isActive
        existing.address = Address().apply {
            street = updatedDto.address.street
            city = updatedDto.address.city
            state = updatedDto.address.state
            zipCode = updatedDto.address.zipCode
            country = updatedDto.address.country
        }

        return Response.ok(DoctorDto.fromEntity(existing)).build()
    }

    @DELETE
    @Path("{id}")
    @Transactional
    fun delete(@PathParam("id") id: UUID): Response {
        val deleted = doctorRepo.deleteById(id)
        return if (deleted) Response.noContent().build() else Response.status(Response.Status.NOT_FOUND).build()
    }
}
