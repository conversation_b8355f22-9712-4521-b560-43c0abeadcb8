package sirobilt.meghasanjivini.masterdata.repository

import com.sirobilt.domain.Specialization
import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepository
import jakarta.enterprise.context.ApplicationScoped
import sirobilt.meghasanjivini.masterdata.model.Doctor
import java.util.*

@ApplicationScoped
class SpecializationRepository : PanacheRepository<Specialization> {

    fun findById(id: UUID): Specialization? =
        find("specializationId", id).firstResult()

    fun findByCode(code: String): Specialization? =
        find("code", code).firstResult()

    fun deleteById(id: UUID): Boolean {
        return delete("specializationId = ?1", id) > 0
    }


}