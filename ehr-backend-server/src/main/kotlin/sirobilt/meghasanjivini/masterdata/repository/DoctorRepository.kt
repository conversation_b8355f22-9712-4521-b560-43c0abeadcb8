package sirobilt.meghasanjivini.masterdata.repository


import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepository
import jakarta.enterprise.context.ApplicationScoped
import sirobilt.meghasanjivini.masterdata.model.Doctor
import java.util.*

@ApplicationScoped
class DoctorRepository : PanacheRepository<Doctor> {

    fun findById(id: UUID): Doctor? =
        find("doctorId", id).firstResult()

    fun findAllActive(): List<Doctor> =
        find("isActive", true).list()

    fun listBySpecialization(specializationId: String): List<Doctor> =
        list("specialization.specializationId", specializationId)

    // Custom delete method for UUID
    fun deleteById(id: UUID): <PERSON><PERSON><PERSON> {
        return delete("doctorId = ?1", id) > 0
    }
}