package sirobilt.meghasanjivini.patientregistration.service

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import sirobilt.meghasanjivini.patientregistration.dto.DuplicateResolutionRequest
import sirobilt.meghasanjivini.patientregistration.dto.DuplicateResolutionResponse
import sirobilt.meghasanjivini.patientregistration.dto.ResolutionAction
import sirobilt.meghasanjivini.patientregistration.model.DuplicateDetectionLogEntity
import sirobilt.meghasanjivini.patientregistration.model.DuplicatePatientRelationshipEntity
import sirobilt.meghasanjivini.patientregistration.repository.DuplicateDetectionLogRepository
import sirobilt.meghasanjivini.patientregistration.repository.DuplicatePatientRelationshipRepository
import sirobilt.meghasanjivini.patientregistration.repository.PatientRepository
import java.time.OffsetDateTime
import java.util.logging.Logger

/**
 * Service for resolving duplicate patient records
 */
@ApplicationScoped
class DuplicateResolutionService {

    @Inject
    lateinit var patientRepository: PatientRepository

    @Inject
    lateinit var relationshipRepository: DuplicatePatientRelationshipRepository

    @Inject
    lateinit var logRepository: DuplicateDetectionLogRepository

    private val logger: Logger = Logger.getLogger(DuplicateResolutionService::class.java.name)

    /**
     * Resolve duplicate patient records based on user decision
     */
    @Transactional
    fun resolveDuplicate(request: DuplicateResolutionRequest): DuplicateResolutionResponse {
        logger.info("Resolving duplicate: ${request.candidatePatientId} vs ${request.duplicatePatientId}")

        try {
            // Validate that both patients exist
            val candidatePatient = patientRepository.findByUpId(request.candidatePatientId)
                ?: return DuplicateResolutionResponse(
                    success = false,
                    message = "Candidate patient not found: ${request.candidatePatientId}"
                )

            val duplicatePatient = patientRepository.findByUpId(request.duplicatePatientId)
                ?: return DuplicateResolutionResponse(
                    success = false,
                    message = "Duplicate patient not found: ${request.duplicatePatientId}"
                )

            // Check if relationship already exists
            if (relationshipRepository.relationshipExists(request.candidatePatientId, request.duplicatePatientId)) {
                return DuplicateResolutionResponse(
                    success = false,
                    message = "Relationship between these patients already exists"
                )
            }

            return when (request.resolutionAction) {
                ResolutionAction.MERGE_RECORDS -> mergePatientRecords(request, candidatePatient.upId, duplicatePatient.upId)
                ResolutionAction.MARK_AS_DIFFERENT -> markAsDifferent(request)
                ResolutionAction.CREATE_NEW_ANYWAY -> createNewAnyway(request)
            }

        } catch (e: Exception) {
            logger.severe("Error resolving duplicate: ${e.message}")
            return DuplicateResolutionResponse(
                success = false,
                message = "Error resolving duplicate: ${e.message}"
            )
        }
    }

    /**
     * Merge two patient records
     */
    private fun mergePatientRecords(
        request: DuplicateResolutionRequest,
        primaryPatientId: String,
        duplicatePatientId: String
    ): DuplicateResolutionResponse {
        logger.info("Merging patient records: $primaryPatientId (primary) <- $duplicatePatientId (duplicate)")

        try {
            val primaryPatient = patientRepository.findByUpId(primaryPatientId)!!
            val duplicatePatient = patientRepository.findByUpId(duplicatePatientId)!!

            // Create relationship record
            val relationship = DuplicatePatientRelationshipEntity(
                primaryPatientId = primaryPatientId,
                duplicatePatientId = duplicatePatientId,
                relationshipType = "CONFIRMED_DUPLICATE",
                confidenceScore = 100, // Manual confirmation
                identifiedBy = request.reviewedBy,
                notes = request.notes,
                identifiedAt = OffsetDateTime.now()
            )
            relationshipRepository.persist(relationship)

            // Merge data from duplicate to primary (only if primary has null/empty values)
            mergePatientData(primaryPatient, duplicatePatient)

            // Soft delete the duplicate patient
            duplicatePatient.softDeleted = true
            patientRepository.persist(duplicatePatient)

            // Update any pending duplicate detection logs
            updateDetectionLogs(duplicatePatientId, "MERGED", request.reviewedBy, request.notes)

            logger.info("Successfully merged patient records: $primaryPatientId <- $duplicatePatientId")

            return DuplicateResolutionResponse(
                success = true,
                message = "Patient records merged successfully",
                primaryPatientId = primaryPatientId,
                mergedPatientId = duplicatePatientId
            )

        } catch (e: Exception) {
            logger.severe("Error merging patient records: ${e.message}")
            throw RuntimeException("Failed to merge patient records", e)
        }
    }

    /**
     * Mark patients as different (false positive)
     */
    private fun markAsDifferent(request: DuplicateResolutionRequest): DuplicateResolutionResponse {
        logger.info("Marking patients as different: ${request.candidatePatientId} vs ${request.duplicatePatientId}")

        try {
            // Create relationship record marking as false positive
            val relationship = DuplicatePatientRelationshipEntity(
                primaryPatientId = request.candidatePatientId,
                duplicatePatientId = request.duplicatePatientId,
                relationshipType = "FALSE_POSITIVE",
                confidenceScore = 0,
                identifiedBy = request.reviewedBy,
                notes = request.notes,
                identifiedAt = OffsetDateTime.now()
            )
            relationshipRepository.persist(relationship)

            // Update detection logs
            updateDetectionLogs(request.candidatePatientId, "MARKED_DIFFERENT", request.reviewedBy, request.notes)

            return DuplicateResolutionResponse(
                success = true,
                message = "Patients marked as different (false positive)"
            )

        } catch (e: Exception) {
            logger.severe("Error marking patients as different: ${e.message}")
            throw RuntimeException("Failed to mark patients as different", e)
        }
    }

    /**
     * Allow creation of new patient anyway
     */
    private fun createNewAnyway(request: DuplicateResolutionRequest): DuplicateResolutionResponse {
        logger.info("Allowing new patient creation: ${request.candidatePatientId}")

        try {
            // Update detection logs to indicate manual approval
            updateDetectionLogs(request.candidatePatientId, "APPROVED_NEW", request.reviewedBy, request.notes)

            return DuplicateResolutionResponse(
                success = true,
                message = "New patient creation approved despite potential duplicates"
            )

        } catch (e: Exception) {
            logger.severe("Error approving new patient creation: ${e.message}")
            throw RuntimeException("Failed to approve new patient creation", e)
        }
    }

    /**
     * Merge data from duplicate patient to primary patient
     */
    private fun mergePatientData(primary: sirobilt.meghasanjivini.patientregistration.model.Patient, 
                                duplicate: sirobilt.meghasanjivini.patientregistration.model.Patient) {
        // Merge basic information (only if primary has null/empty values)
        if (primary.middleName.isNullOrBlank() && !duplicate.middleName.isNullOrBlank()) {
            primary.middleName = duplicate.middleName
        }
        if (primary.dateOfBirth == null && duplicate.dateOfBirth != null) {
            primary.dateOfBirth = duplicate.dateOfBirth
        }
        if (primary.age == null && duplicate.age != null) {
            primary.age = duplicate.age
        }
        if (primary.bloodGroup == null && duplicate.bloodGroup != null) {
            primary.bloodGroup = duplicate.bloodGroup
        }
        if (primary.maritalStatus == null && duplicate.maritalStatus != null) {
            primary.maritalStatus = duplicate.maritalStatus
        }
        if (primary.citizenship.isNullOrBlank() && !duplicate.citizenship.isNullOrBlank()) {
            primary.citizenship = duplicate.citizenship
        }
        if (primary.religion.isNullOrBlank() && !duplicate.religion.isNullOrBlank()) {
            primary.religion = duplicate.religion
        }
        if (primary.occupation.isNullOrBlank() && !duplicate.occupation.isNullOrBlank()) {
            primary.occupation = duplicate.occupation
        }
        if (primary.education.isNullOrBlank() && !duplicate.education.isNullOrBlank()) {
            primary.education = duplicate.education
        }

        // Merge contacts (add unique contacts from duplicate)
        for (duplicateContact in duplicate.contacts) {
            val existingContact = primary.contacts.find { 
                it.phoneNumber == duplicateContact.phoneNumber || 
                it.email == duplicateContact.email 
            }
            if (existingContact == null) {
                val newContact = sirobilt.meghasanjivini.patientregistration.model.PatientContact(
                    patient = primary,
                    mobileNumber = duplicateContact.mobileNumber,
                    phoneNumber = duplicateContact.phoneNumber,
                    email = duplicateContact.email,
                    preferredContactMode = duplicateContact.preferredContactMode,
                    phoneContactPreference = duplicateContact.phoneContactPreference,
                    consentToShare = duplicateContact.consentToShare
                )
                primary.contacts.add(newContact)
            }
        }

        // Merge addresses (add unique addresses from duplicate)
        for (duplicateAddress in duplicate.addresses) {
            val existingAddress = primary.addresses.find { 
                it.houseNoOrFlatNo == duplicateAddress.houseNoOrFlatNo &&
                it.cityOrVillage == duplicateAddress.cityOrVillage &&
                it.pincode == duplicateAddress.pincode
            }
            if (existingAddress == null) {
                val newAddress = sirobilt.meghasanjivini.patientregistration.model.PatientAddress(
                    patient = primary,
                    addressType = duplicateAddress.addressType,
                    houseNoOrFlatNo = duplicateAddress.houseNoOrFlatNo,
                    localityOrSector = duplicateAddress.localityOrSector,
                    cityOrVillage = duplicateAddress.cityOrVillage,
                    pincode = duplicateAddress.pincode,
                    districtId = duplicateAddress.districtId,
                    stateId = duplicateAddress.stateId,
                    country = duplicateAddress.country
                )
                primary.addresses.add(newAddress)
            }
        }

        // Merge ABHA information if primary doesn't have it
        if (primary.abha == null && duplicate.abha != null) {
            val newAbha = sirobilt.meghasanjivini.patientregistration.model.PatientAbha(
                patient = primary,
                abhaNumber = duplicate.abha!!.abhaNumber,
                abhaAddress = duplicate.abha!!.abhaAddress
            )
            primary.abha = newAbha
        }

        // Merge insurance information if primary doesn't have it
        if (primary.insurance == null && duplicate.insurance != null) {
            val newInsurance = sirobilt.meghasanjivini.patientregistration.model.PatientInsurance(
                patient = primary,
                insuranceProvider = duplicate.insurance!!.insuranceProvider,
                policyNumber = duplicate.insurance!!.policyNumber,
                policyStartDate = duplicate.insurance!!.policyStartDate,
                policyEndDate = duplicate.insurance!!.policyEndDate,
                coverageAmount = duplicate.insurance!!.coverageAmount
            )
            primary.insurance = newInsurance
        }

        patientRepository.persist(primary)
    }

    /**
     * Update detection logs with resolution information
     */
    private fun updateDetectionLogs(patientId: String, action: String, reviewedBy: String, notes: String?) {
        val logs = logRepository.findByPatientId(patientId)
        for (log in logs) {
            if (log.reviewTime == null) { // Only update unreviewed logs
                log.actionTaken = action
                log.reviewedBy = reviewedBy
                log.reviewNotes = notes
                log.reviewTime = OffsetDateTime.now()
                logRepository.persist(log)
            }
        }
    }

    /**
     * Get pending duplicate resolutions
     */
    fun getPendingResolutions(): List<sirobilt.meghasanjivini.patientregistration.model.DuplicateDetectionLogEntity> {
        return logRepository.findPendingReview()
    }

    /**
     * Get duplicate relationships for a patient
     */
    fun getPatientRelationships(patientId: String): List<DuplicatePatientRelationshipEntity> {
        val primaryRelationships = relationshipRepository.findByPrimaryPatientId(patientId)
        val duplicateRelationships = relationshipRepository.findByDuplicatePatientId(patientId)
        return primaryRelationships + duplicateRelationships
    }
}
