package sirobilt.meghasanjivini.patientregistration.service

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import org.eclipse.microprofile.config.inject.ConfigProperty
import sirobilt.meghasanjivini.patientregistration.controller.UpidConfigValidationRequest
import sirobilt.meghasanjivini.patientregistration.controller.UpidTestSampleRequest
import sirobilt.meghasanjivini.patientregistration.repository.PatientRepository
import java.time.OffsetDateTime
import java.util.logging.Logger

/**
 * Service for managing UPID (Unique Patient ID) configuration and generation
 */
@ApplicationScoped
class UpidConfigurationService {

    @Inject
    lateinit var patientRepository: PatientRepository

    @ConfigProperty(name = "upid.format.type", defaultValue = "STANDARD")
    lateinit var defaultFormatType: String

    @ConfigProperty(name = "upid.format.standard.facility-digits", defaultValue = "3")
    lateinit var defaultFacilityDigits: String

    @ConfigProperty(name = "upid.format.standard.separator", defaultValue = "-")
    lateinit var defaultSeparator: String

    private val logger: Logger = Logger.getLogger(UpidConfigurationService::class.java.name)

    /**
     * Get current UPID configuration
     */
    fun getCurrentConfiguration(): Map<String, Any> {
        return mapOf(
            "formatType" to defaultFormatType,
            "facilityDigits" to defaultFacilityDigits.toInt(),
            "separator" to defaultSeparator,
            "pattern" to "XXX-00-0000-0001",
            "description" to "Standard UPID format with facility prefix",
            "lastUpdated" to OffsetDateTime.now(),
            "isActive" to true
        )
    }

    /**
     * Get available UPID formats
     */
    fun getAvailableFormats(): List<Map<String, Any>> {
        return listOf(
            mapOf(
                "type" to "STANDARD",
                "description" to "Standard format: XXX-00-0000-0001",
                "pattern" to "XXX-00-0000-NNNN",
                "example" to "001-00-0000-0001",
                "facilityDigits" to 3,
                "separator" to "-"
            ),
            mapOf(
                "type" to "COMPACT",
                "description" to "Compact format: XXX00000001",
                "pattern" to "XXXNNNNNNN",
                "example" to "00100000001",
                "facilityDigits" to 3,
                "separator" to ""
            ),
            mapOf(
                "type" to "EXTENDED",
                "description" to "Extended format: XXXX-00-0000-0001",
                "pattern" to "XXXX-00-0000-NNNN",
                "example" to "0001-00-0000-0001",
                "facilityDigits" to 4,
                "separator" to "-"
            )
        )
    }

    /**
     * UPID configuration health check
     */
    fun healthCheck(): Map<String, Any> {
        return try {
            val config = getCurrentConfiguration()
            val sampleUpid = generateSampleUpid("1")

            mapOf<String, Any>(
                "status" to "HEALTHY",
                "configurationLoaded" to true,
                "formatType" to (config["formatType"] ?: "UNKNOWN"),
                "sampleGeneration" to "WORKING",
                "sampleUpid" to sampleUpid,
                "timestamp" to OffsetDateTime.now()
            )
        } catch (e: Exception) {
            logger.severe("UPID health check failed: ${e.message}")
            mapOf<String, Any>(
                "status" to "UNHEALTHY",
                "error" to (e.message ?: "Unknown error"),
                "timestamp" to OffsetDateTime.now()
            )
        }
    }

    /**
     * Generate sample UPID for a facility
     */
    fun generateSampleUpid(facilityId: String): String {
        if (facilityId.isBlank()) {
            throw IllegalArgumentException("Facility ID cannot be blank")
        }

        return try {
            val facilityIdInt = facilityId.toIntOrNull() ?: throw IllegalArgumentException("Facility ID must be numeric")
            
            // Get the last MRN for this facility to determine next sequence
            val lastMrn = patientRepository.findLastMrnForFacility(facilityId)
            val nextSequence = if (lastMrn != null) {
                // Extract sequence number from last MRN and increment
                val parts = lastMrn.split("-")
                if (parts.size >= 4) {
                    val lastSequence = parts[3].toIntOrNull() ?: 0
                    lastSequence + 1
                } else {
                    1
                }
            } else {
                1
            }

            // Generate UPID in standard format: XXX-00-0000-NNNN
            val paddedFacilityId = facilityId.padStart(3, '0')
            val paddedSequence = nextSequence.toString().padStart(4, '0')
            
            "$paddedFacilityId-00-0000-$paddedSequence"
        } catch (e: NumberFormatException) {
            throw IllegalArgumentException("Invalid facility ID format: $facilityId")
        }
    }

    /**
     * Validate UPID format
     */
    fun validateUpid(upid: String): Map<String, Any> {
        if (upid.isBlank()) {
            return mapOf(
                "valid" to false,
                "error" to "UPID cannot be blank",
                "upid" to upid
            )
        }

        return try {
            // Standard format validation: XXX-00-0000-NNNN
            val pattern = Regex("^\\d{3}-\\d{2}-\\d{4}-\\d{4}$")
            val isValid = pattern.matches(upid)
            
            if (isValid) {
                val parts = upid.split("-")
                mapOf(
                    "valid" to true,
                    "upid" to upid,
                    "format" to "STANDARD",
                    "facilityId" to parts[0],
                    "sequence" to parts[3],
                    "pattern" to "XXX-00-0000-NNNN"
                )
            } else {
                mapOf(
                    "valid" to false,
                    "error" to "UPID does not match standard format XXX-00-0000-NNNN",
                    "upid" to upid,
                    "expectedPattern" to "XXX-00-0000-NNNN"
                )
            }
        } catch (e: Exception) {
            mapOf(
                "valid" to false,
                "error" to "Error validating UPID: ${e.message}",
                "upid" to upid
            )
        }
    }

    /**
     * Get effective configuration (admin)
     */
    fun getEffectiveConfiguration(): Map<String, Any> {
        val currentConfig = getCurrentConfiguration()
        return currentConfig + mapOf<String, Any>(
            "source" to "APPLICATION_PROPERTIES",
            "overrides" to emptyMap<String, String>(),
            "environment" to "DEVELOPMENT",
            "configurationKeys" to listOf(
                "upid.format.type",
                "upid.format.standard.facility-digits",
                "upid.format.standard.separator"
            )
        )
    }

    /**
     * Get configuration history (admin)
     */
    fun getConfigurationHistory(): List<Map<String, Any>> {
        // For now, return a mock history since we don't have a configuration history table
        return listOf(
            mapOf(
                "timestamp" to OffsetDateTime.now().minusDays(1),
                "action" to "SYSTEM_INITIALIZATION",
                "changes" to mapOf("formatType" to "STANDARD"),
                "user" to "SYSTEM",
                "version" to "1.0.0"
            )
        )
    }

    /**
     * Validate configuration changes (admin)
     */
    fun validateConfiguration(request: UpidConfigValidationRequest): Map<String, Any> {
        val validationResults = mutableMapOf<String, Any>()
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        request.changes.forEach { (key, value) ->
            when (key) {
                "upid.format.standard.facility-digits" -> {
                    val digits = value.toIntOrNull()
                    if (digits == null || digits < 2 || digits > 5) {
                        errors.add("Facility digits must be between 2 and 5")
                    }
                }
                "upid.format.standard.separator" -> {
                    if (value.length > 1) {
                        warnings.add("Separator should be a single character")
                    }
                }
                "upid.format.type" -> {
                    if (value !in listOf("STANDARD", "COMPACT", "EXTENDED")) {
                        errors.add("Invalid format type: $value")
                    }
                }
                else -> {
                    warnings.add("Unknown configuration key: $key")
                }
            }
        }

        return mapOf(
            "valid" to errors.isEmpty(),
            "errors" to errors,
            "warnings" to warnings,
            "changes" to request.changes,
            "validateOnly" to request.validateOnly,
            "timestamp" to OffsetDateTime.now()
        )
    }

    /**
     * Generate test samples with custom configuration (admin)
     */
    fun generateTestSamples(facilityId: String, request: UpidTestSampleRequest): Map<String, Any> {
        val samples = mutableListOf<String>()
        val sampleCount = minOf(request.sampleCount, 10) // Limit to 10 samples max

        repeat(sampleCount) { index ->
            val sequence = index + 1
            val paddedFacilityId = facilityId.padStart(3, '0')
            val paddedSequence = sequence.toString().padStart(4, '0')
            samples.add("$paddedFacilityId-00-0000-$paddedSequence")
        }

        return mapOf(
            "facilityId" to facilityId,
            "sampleCount" to sampleCount,
            "samples" to samples,
            "configuration" to (request.tempConfig ?: getCurrentConfiguration()),
            "timestamp" to OffsetDateTime.now()
        )
    }
}
