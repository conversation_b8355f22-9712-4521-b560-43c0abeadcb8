package sirobilt.meghasanjivini.patientregistration.controller

import jakarta.inject.Inject
import jakarta.validation.Valid
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import org.eclipse.microprofile.openapi.annotations.Operation
import org.eclipse.microprofile.openapi.annotations.tags.Tag
import sirobilt.meghasanjivini.patientregistration.service.UpidConfigurationService
import java.util.logging.Logger

/**
 * REST Controller for UPID (Unique Patient ID) configuration management
 */
@Path("/upid-config")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "UPID Configuration", description = "Unique Patient ID configuration and generation APIs")
class UpidConfigurationController {

    @Inject
    lateinit var upidConfigService: UpidConfigurationService

    private val logger: Logger = Logger.getLogger(UpidConfigurationController::class.java.name)

    /**
     * Get current UPID configuration
     */
    @GET
    @Path("/current")
    @Operation(summary = "Get current UPID configuration", description = "Get the current UPID format configuration")
    fun getCurrentConfiguration(): Response {
        return try {
            val config = upidConfigService.getCurrentConfiguration()
            Response.ok(config).build()
        } catch (e: Exception) {
            logger.severe("Error retrieving UPID configuration: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to retrieve UPID configuration"))
                .build()
        }
    }

    /**
     * Get available UPID formats
     */
    @GET
    @Path("/formats")
    @Operation(summary = "Get available UPID formats", description = "Get list of available UPID format types")
    fun getAvailableFormats(): Response {
        return try {
            val formats = upidConfigService.getAvailableFormats()
            Response.ok(formats).build()
        } catch (e: Exception) {
            logger.severe("Error retrieving UPID formats: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to retrieve UPID formats"))
                .build()
        }
    }

    /**
     * UPID configuration health check
     */
    @GET
    @Path("/health")
    @Operation(summary = "UPID configuration health check", description = "Check if UPID configuration is healthy")
    fun healthCheck(): Response {
        return try {
            val health = upidConfigService.healthCheck()
            Response.ok(health).build()
        } catch (e: Exception) {
            logger.severe("UPID health check failed: ${e.message}")
            Response.status(Response.Status.SERVICE_UNAVAILABLE)
                .entity(mapOf("error" to "UPID configuration health check failed"))
                .build()
        }
    }

    /**
     * Generate sample UPID for a facility
     */
    @POST
    @Path("/generate-sample/{facilityId}")
    @Operation(summary = "Generate sample UPID", description = "Generate a sample UPID for the specified facility")
    fun generateSampleUpid(@PathParam("facilityId") facilityId: String): Response {
        return try {
            val sampleUpid = upidConfigService.generateSampleUpid(facilityId)
            Response.ok(mapOf("upid" to sampleUpid, "facilityId" to facilityId)).build()
        } catch (e: IllegalArgumentException) {
            logger.warning("Invalid facility ID for UPID generation: ${e.message}")
            Response.status(Response.Status.BAD_REQUEST)
                .entity(mapOf("error" to e.message))
                .build()
        } catch (e: Exception) {
            logger.severe("Error generating sample UPID: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to generate sample UPID"))
                .build()
        }
    }

    /**
     * Validate UPID format
     */
    @POST
    @Path("/validate")
    @Operation(summary = "Validate UPID format", description = "Validate if a UPID follows the correct format")
    fun validateUpid(@Valid request: UpidValidationRequest): Response {
        return try {
            val validation = upidConfigService.validateUpid(request.upid)
            Response.ok(validation).build()
        } catch (e: Exception) {
            logger.severe("Error validating UPID: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to validate UPID"))
                .build()
        }
    }

    /**
     * Get effective configuration (Admin endpoint)
     */
    @GET
    @Path("/admin/effective")
    @Operation(summary = "Get effective configuration", description = "Get the effective UPID configuration (admin)")
    fun getEffectiveConfiguration(): Response {
        return try {
            val config = upidConfigService.getEffectiveConfiguration()
            Response.ok(config).build()
        } catch (e: Exception) {
            logger.severe("Error retrieving effective UPID configuration: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to retrieve effective configuration"))
                .build()
        }
    }

    /**
     * Get configuration history (Admin endpoint)
     */
    @GET
    @Path("/admin/history")
    @Operation(summary = "Get configuration history", description = "Get UPID configuration change history")
    fun getConfigurationHistory(): Response {
        return try {
            val history = upidConfigService.getConfigurationHistory()
            Response.ok(history).build()
        } catch (e: Exception) {
            logger.severe("Error retrieving UPID configuration history: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to retrieve configuration history"))
                .build()
        }
    }

    /**
     * Validate configuration changes (Admin endpoint)
     */
    @POST
    @Path("/admin/validate")
    @Operation(summary = "Validate configuration changes", description = "Validate UPID configuration changes without applying them")
    fun validateConfiguration(@Valid request: UpidConfigValidationRequest): Response {
        return try {
            val validation = upidConfigService.validateConfiguration(request)
            Response.ok(validation).build()
        } catch (e: Exception) {
            logger.severe("Error validating UPID configuration: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to validate configuration"))
                .build()
        }
    }

    /**
     * Generate test samples with custom configuration (Admin endpoint)
     */
    @POST
    @Path("/admin/test-samples/{facilityId}")
    @Operation(summary = "Generate test samples", description = "Generate test UPID samples with custom configuration")
    fun generateTestSamples(
        @PathParam("facilityId") facilityId: String,
        @Valid request: UpidTestSampleRequest
    ): Response {
        return try {
            val samples = upidConfigService.generateTestSamples(facilityId, request)
            Response.ok(samples).build()
        } catch (e: IllegalArgumentException) {
            logger.warning("Invalid request for test samples: ${e.message}")
            Response.status(Response.Status.BAD_REQUEST)
                .entity(mapOf("error" to e.message))
                .build()
        } catch (e: Exception) {
            logger.severe("Error generating test samples: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to generate test samples"))
                .build()
        }
    }
}

/**
 * Request DTOs for UPID validation and configuration
 */
data class UpidValidationRequest(
    val upid: String
)

data class UpidConfigValidationRequest(
    val changes: Map<String, String>,
    val validateOnly: Boolean = true
)

data class UpidTestSampleRequest(
    val facilityId: String,
    val sampleCount: Int = 3,
    val tempConfig: Map<String, String>? = null
)
