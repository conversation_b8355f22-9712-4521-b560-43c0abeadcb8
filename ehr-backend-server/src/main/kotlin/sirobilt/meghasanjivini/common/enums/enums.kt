package sirobilt.meghasanjivini.common.enums

enum class AppointmentType(val value: String) {
    CONSULTATION("Consultation"),
    FOLLOW_UP("FollowUp"),
    EMERGENCY("Emergency"),
    PROCEDURE("Procedure"),
    SURGERY("Surgery"),
    DIAGNOSTIC("Diagnostic"),
    VACCINATION("Vaccination"),
    CHECKUP("Checkup");

    companion object {
        fun fromValue(value: String): AppointmentType? =
            values().find { it.value == value }
    }
}

enum class RecurringPattern(val value: String) {
    DAILY("Daily"),
    WEEKLY("Weekly"),
    MONTHLY("Monthly"),
    YEARLY("Yearly");

    companion object {
        fun fromValue(value: String): RecurringPattern? =
            values().find { it.value == value }
    }
}