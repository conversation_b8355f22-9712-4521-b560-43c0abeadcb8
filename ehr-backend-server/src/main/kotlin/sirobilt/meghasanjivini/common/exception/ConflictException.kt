package sirobilt.meghasanjivini.common.exception

import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jakarta.ws.rs.ext.ExceptionMapper
import jakarta.ws.rs.ext.Provider

@Provider
class ConflictExceptionMapper : ExceptionMapper<ConflictException> {
    override fun toResponse(exception: ConflictException): Response {
        val errorResponse = mapOf(
            "error" to "Conflict",
            "message" to exception.message
        )

        return Response.status(Response.Status.CONFLICT)
            .type(MediaType.APPLICATION_JSON)
            .entity(errorResponse)
            .build()
    }
}


class ConflictException(message: String) : RuntimeException(message)
