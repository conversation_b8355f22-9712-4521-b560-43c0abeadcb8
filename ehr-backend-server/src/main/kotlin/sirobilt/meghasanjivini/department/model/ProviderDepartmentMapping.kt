package sirobilt.meghasanjivini.department.model

import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

@Entity
@Table(name = "provider_department_mappings")
class ProviderDepartmentMapping : PanacheEntityBase {

    @Id
    var mappingId: String = UUID.randomUUID().toString()

    var providerId: String = ""
    var departmentId: String = ""
    var facilityId: String = ""

    var role: String = "CONSULTANT"
    var isPrimary: Boolean = false

    var effectiveFrom: LocalDate = LocalDate.now()
    var effectiveTo: LocalDate? = null

    var isActive: Boolean = true

    var createdAt: LocalDateTime = LocalDateTime.now()
    var updatedAt: LocalDateTime = LocalDateTime.now()
    var assignedBy: String? = null
}
