package sirobilt.meghasanjivini.department.model

import jakarta.persistence.*
import java.math.BigDecimal
import java.time.LocalDateTime

@Entity
@Table(name = "department_services")
class DepartmentService {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null

    @Column(name = "department_id", nullable = false)
    lateinit var departmentId: String

    @Column(name = "service_name", nullable = false)
    lateinit var serviceName: String

    @Column(name = "service_code")
    var serviceCode: String? = null

    @Column(name = "description")
    var description: String? = null

    @Column(name = "average_service_time")
    var averageServiceTime: Int = 15

    @Column(name = "max_concurrent_patients")
    var maxConcurrentPatients: Int = 1

    @Column(name = "requires_appointment")
    var requiresAppointment: Boolean = true

    @Column(name = "allows_walk_in")
    var allowsWalkIn: Boolean = false

    @Column(name = "base_price")
    var basePrice: BigDecimal? = null

    @Column(name = "currency")
    var currency: String = "INR"

    @Column(name = "is_active")
    var isActive: Boolean = true

    @Column(name = "created_at")
    var createdAt: LocalDateTime = LocalDateTime.now()

    @Column(name = "updated_at")
    var updatedAt: LocalDateTime = LocalDateTime.now()
}
