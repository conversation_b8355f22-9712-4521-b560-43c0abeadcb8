package sirobilt.meghasanjivini.department.model

import com.fasterxml.jackson.annotation.JsonBackReference
import jakarta.persistence.*
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDateTime
import java.time.LocalTime

@Entity
@Table(
    name = "department_operating_hours",
    uniqueConstraints = [UniqueConstraint(columnNames = ["department_id", "day_of_week"])]
)
class DepartmentOperatingHours {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null

    @Enumerated(EnumType.STRING)
    @Column(name = "day_of_week", nullable = false)
    lateinit var dayOfWeek: DayOfWeek

    @Column(name = "is_operating")
    var isOperating: Boolean = true

    @Column(name = "start_time", nullable = false)
    lateinit var startTime: LocalTime

    @Column(name = "end_time", nullable = false)
    lateinit var endTime: LocalTime

    @Column(name = "break_start_time")
    var breakStartTime: LocalTime? = null

    @Column(name = "break_end_time")
    var breakEndTime: LocalTime? = null

    @Column(name = "emergency_hours")
    var emergencyHours: Boolean = false

    @Column(name = "created_at")
    var createdAt: LocalDateTime = LocalDateTime.now()

    @Column(name = "updated_at")
    var updatedAt: LocalDateTime = LocalDateTime.now()

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "department_id")
    @JsonBackReference
    var department: Department? = null
}
