package sirobilt.meghasanjivini.department.repository

import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepositoryBase
import jakarta.enterprise.context.ApplicationScoped
import sirobilt.meghasanjivini.department.model.Department
import sirobilt.meghasanjivini.department.model.ProviderDepartmentMapping

@ApplicationScoped
class DepartmentRepository : PanacheRepositoryBase<Department, String>

@ApplicationScoped
class ProviderDepartmentMappingRepository : PanacheRepositoryBase<ProviderDepartmentMapping, String>
