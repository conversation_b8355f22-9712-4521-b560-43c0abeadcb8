package sirobilt.meghasanjivini.department.service

import jakarta.enterprise.context.ApplicationScoped
import sirobilt.meghasanjivini.department.dto.DepartmentServiceDTO
import sirobilt.meghasanjivini.department.model.DepartmentService
import sirobilt.meghasanjivini.department.repository.DepartmentServiceRepository

@ApplicationScoped
class DepartmentServiceService(
    private val repository: DepartmentServiceRepository
) {
    fun create(dto: DepartmentServiceDTO): DepartmentService {
        val entity = DepartmentService()
        entity.departmentId = dto.departmentId
        entity.serviceName = dto.serviceName
        entity.serviceCode = dto.serviceCode
        entity.description = dto.description
        entity.averageServiceTime = dto.averageServiceTime
        entity.maxConcurrentPatients = dto.maxConcurrentPatients
        entity.requiresAppointment = dto.requiresAppointment
        entity.allowsWalkIn = dto.allowsWalkIn
        entity.basePrice = dto.basePrice
        entity.currency = dto.currency
        entity.isActive = dto.isActive

        repository.persist(entity)
        return entity
    }

    fun getByDepartment(departmentId: String): List<DepartmentService> =
        repository.list("departmentId", departmentId)
}
