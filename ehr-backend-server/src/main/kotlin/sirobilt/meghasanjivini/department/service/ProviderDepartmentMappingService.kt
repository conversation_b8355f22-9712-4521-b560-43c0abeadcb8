package sirobilt.meghasanjivini.department.service

import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional
import jakarta.ws.rs.NotFoundException
import sirobilt.meghasanjivini.department.dto.ProviderDepartmentMappingDTO
import sirobilt.meghasanjivini.department.model.ProviderDepartmentMapping
import sirobilt.meghasanjivini.department.repository.ProviderDepartmentMappingRepository
import java.time.LocalDateTime
import java.util.UUID

@ApplicationScoped
class ProviderDepartmentMappingService(
    private val repository: ProviderDepartmentMappingRepository
) {
    fun getAll(): List<ProviderDepartmentMapping> = repository.listAll()


    @Transactional
    fun create(dto: ProviderDepartmentMappingDTO): ProviderDepartmentMapping {
        val entity = ProviderDepartmentMapping().apply {
            mappingId = UUID.randomUUID().toString()
            providerId = dto.providerId
            departmentId = dto.departmentId
            facilityId = dto.facilityId
            role = dto.role.toString()
            isPrimary = dto.isPrimary
            effectiveFrom = dto.effectiveFrom
            effectiveTo = dto.effectiveTo
            isActive = dto.isActive
        }
        repository.persist(entity)
        return entity
    }

    @Transactional
    fun update(id: String, dto: ProviderDepartmentMappingDTO): ProviderDepartmentMapping {
        val mapping = repository.findById(id)?: throw NotFoundException("ProviderDepartmentMapping not found")
        mapping.role = dto.role.toString()
        mapping.isPrimary = dto.isPrimary
        mapping.effectiveFrom = dto.effectiveFrom
        mapping.effectiveTo = dto.effectiveTo
        mapping.isActive = dto.isActive
        mapping.updatedAt = LocalDateTime.now()
        return mapping
    }


    @Transactional
    fun delete(id: String) {
        repository.deleteById(id)
    }
}
