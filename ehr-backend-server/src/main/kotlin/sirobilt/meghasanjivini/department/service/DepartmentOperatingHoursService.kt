package sirobilt.meghasanjivini.department.service

import jakarta.enterprise.context.ApplicationScoped
import sirobilt.meghasanjivini.department.dto.DepartmentOperatingHoursDTO
import sirobilt.meghasanjivini.department.model.DepartmentOperatingHours
import sirobilt.meghasanjivini.department.repository.DepartmentOperatingHoursRepository

@ApplicationScoped
class DepartmentOperatingHoursService(
    private val repository: DepartmentOperatingHoursRepository
) {
    fun create(dto: DepartmentOperatingHoursDTO): DepartmentOperatingHours {
        val entity = DepartmentOperatingHours()

        entity.dayOfWeek = dto.dayOfWeek
        entity.isOperating = dto.isOperating
        entity.startTime = dto.startTime
        entity.endTime = dto.endTime
        entity.breakStartTime = dto.breakStartTime
        entity.breakEndTime = dto.breakEndTime

        repository.persist(entity)
        return entity
    }

    fun getByDepartment(departmentId: String): List<DepartmentOperatingHours> =
        repository.list("departmentId", departmentId)
}
