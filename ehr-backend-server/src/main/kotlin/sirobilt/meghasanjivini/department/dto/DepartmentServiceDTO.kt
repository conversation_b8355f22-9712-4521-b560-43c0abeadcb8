package sirobilt.meghasanjivini.department.dto

import java.math.BigDecimal

data class DepartmentServiceDTO(
    val departmentId: String,
    val serviceName: String,
    val serviceCode: String?,
    val description: String?,
    val averageServiceTime: Int,
    val maxConcurrentPatients: Int,
    val requiresAppointment: Boolean,
    val allowsWalkIn: Boolean,
    val basePrice: BigDecimal?,
    val currency: String,
    val isActive: Boolean
)