package sirobilt.meghasanjivini.department.dto

import sirobilt.meghasanjivini.department.enums.DepartmentRole
import java.time.LocalDate

data class ProviderDepartmentMappingDTO(
    val mappingId: String?,
    val providerId: String,
    val departmentId: String,
    val facilityId: String,
    val role: DepartmentRole,
    val isPrimary: Boolean = false,
    val effectiveFrom: LocalDate,
    val effectiveTo: LocalDate?,
    val isActive: Boolean = true
)