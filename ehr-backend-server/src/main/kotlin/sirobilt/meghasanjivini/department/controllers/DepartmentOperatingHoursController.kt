package sirobilt.meghasanjivini.department.controllers


import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import org.eclipse.microprofile.openapi.annotations.Operation
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter
import org.eclipse.microprofile.openapi.annotations.tags.Tag
import sirobilt.meghasanjivini.department.dto.DepartmentOperatingHoursDTO
import sirobilt.meghasanjivini.department.model.DepartmentOperatingHours
import sirobilt.meghasanjivini.department.service.DepartmentOperatingHoursService

@Path("/department-operating-hours")
@Tag(name = "Department Operating Hours", description = "Manage operating hours for departments")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
class DepartmentOperatingHoursController(
    private val service: DepartmentOperatingHoursService
) {

    @POST
    @Operation(summary = "Create operating hours for a department")

    fun create(dto: DepartmentOperatingHoursDTO): Response {
        val created = service.create(dto)
        return Response.status(Response.Status.CREATED).entity(created).build()
    }

    @GET
    @Path("/{departmentId}")
    @Operation(summary = "Get operating hours by department ID")

    fun getByDepartment(
        @Parameter(description = "Department ID") @PathParam("departmentId") departmentId: String
    ): List<DepartmentOperatingHours> = service.getByDepartment(departmentId)
}
