package sirobilt.meghasanjivini.department.controllers

import jakarta.ws.rs.*
import jakarta.ws.rs.core.*
import sirobilt.meghasanjivini.department.dto.ProviderDepartmentMappingDTO
import sirobilt.meghasanjivini.department.service.ProviderDepartmentMappingService

@Path("/provider-department-mappings")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
class ProviderDepartmentMappingController(
    private val service: ProviderDepartmentMappingService
) {
    @GET
    fun getAll(): Response = Response.ok(service.getAll()).build()

    @POST
    fun create(dto: ProviderDepartmentMappingDTO): Response = Response.status(Response.Status.CREATED).entity(service.create(dto)).build()

    @PUT
    @Path("/{id}")
    fun update(@PathParam("id") id: String, dto: ProviderDepartmentMappingDTO): Response = Response.ok(service.update(id, dto)).build()

    @DELETE
    @Path("/{id}")
    fun delete(@PathParam("id") id: String): Response {
        service.delete(id)
        return Response.noContent().build()
    }
}
