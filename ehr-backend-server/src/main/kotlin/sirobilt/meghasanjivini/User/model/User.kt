package sirobilt.meghasanjivini.User.model

import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.util.UUID

@Entity
@Table(name = "users")
class User: PanacheEntityBase{

    @Id
    @GeneratedValue(strategy = GenerationType.UUID) // ✅ Modern UUID generation
    @Column(name = "id", updatable = false, nullable = false)
    lateinit var id: UUID



    @Column(nullable = false, unique = true)
    lateinit var  username: String

    @Column(nullable = false, unique = true)
    lateinit var email: String

    @Column(nullable = true)
    lateinit var password: String

    @Column(nullable = false)
    lateinit var facility: String
}