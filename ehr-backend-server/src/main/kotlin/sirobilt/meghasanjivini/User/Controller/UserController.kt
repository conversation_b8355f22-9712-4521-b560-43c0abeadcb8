package sirobilt.meghasanjivini.User.Controller

import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import org.eclipse.microprofile.openapi.annotations.Operation
import org.eclipse.microprofile.openapi.annotations.media.Content
import org.eclipse.microprofile.openapi.annotations.media.Schema
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses
import org.eclipse.microprofile.openapi.annotations.tags.Tag
import sirobilt.meghasanjivini.User.Repository.UserRepository

@Path("/facility")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Facility", description = "Fetch facility information by username or email")
class UserController {

    @Inject
    lateinit var userRepository: UserRepository

    @GET
    @Path("/{identifier}")
    @Operation(
        summary = "Get facility by username or email",
        description = "Returns the facility associated with the given username or email"
    )
    @APIResponses(
        value = [
            APIResponse(
                responseCode = "200",
                description = "Facility found",
                content = [Content(mediaType = "application/json", schema = Schema(implementation = FacilityResponse::class))]
            ),
            APIResponse(
                responseCode = "404",
                description = "User not found"
            )
        ]
    )
    fun getFacility(@PathParam("identifier") identifier: String): Response {
        val user = userRepository.findByUsernameOrEmail(identifier)
        return if (user != null) {
            Response.ok(FacilityResponse(user.facility)).build()
        } else {
            Response.status(Response.Status.NOT_FOUND).entity("User not found").build()
        }
    }

    data class FacilityResponse(val facility: String)
}
