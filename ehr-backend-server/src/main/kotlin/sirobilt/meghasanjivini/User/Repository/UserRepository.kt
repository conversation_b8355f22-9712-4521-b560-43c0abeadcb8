package sirobilt.meghasanjivini.User.Repository

import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepositoryBase
import jakarta.enterprise.context.ApplicationScoped
import sirobilt.meghasanjivini.User.model.User
import java.util.UUID

@ApplicationScoped
class UserRepository : PanacheRepositoryBase<User, UUID> {
    fun findByUsernameOrEmail(input: String): User? {
        return find("username = ?1 or email = ?1", input).firstResult()
    }
}
