package sirobilt.meghasanjivini.appointment

import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepositoryBase
import jakarta.enterprise.context.ApplicationScoped
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import jakarta.transaction.Transactional
import jakarta.ws.rs.WebApplicationException
import sirobilt.meghasanjivini.common.enums.AppointmentType
import sirobilt.meghasanjivini.patientregistration.model.Patient
import java.time.LocalDate
import java.time.LocalDateTime


@ApplicationScoped
class AppointmentRepository: PanacheRepositoryBase<AppointmentEntity, String>{
    @PersistenceContext
    lateinit var em: EntityManager

    fun searchAppointments(
        patientId: String?,
        providerId: String?,
        facilityId: String?,
        status: List<String>?,
        type: List<String>?,
        priority: List<String>?,
        dateFrom: String?,
        dateTo: String?,
        searchTerm: String?,
        page: Int,
        size: Int
    ): Pair<List<AppointmentEntity>, Long> {
        val query = StringBuilder("SELECT a FROM AppointmentEntity a WHERE 1=1")
        val params = mutableMapOf<String, Any>()

        patientId?.let {
            query.append(" AND a.patient.upId = :patientId")
            params["patientId"] = it
        }
        providerId?.let {
            query.append(" AND a.providerId = :providerId")
            params["providerId"] = it
        }
        facilityId?.let {
            query.append(" AND a.facilityId.hospitalId = :facilityId")
            params["facilityId"] = it
        }
        status?.takeIf { it.isNotEmpty() }?.let {
            query.append(" AND a.status IN :status")
            params["status"] = it
        }
        type?.takeIf { it.isNotEmpty() }?.let {
            query.append(" AND a.type IN :type")
            params["type"] = it
        }
        priority?.takeIf { it.isNotEmpty() }?.let {
            query.append(" AND a.priority IN :priority")
            params["priority"] = it
        }
        dateFrom?.let {
            query.append(" AND a.appointmentDate >= :dateFrom")
            params["dateFrom"] = java.time.LocalDate.parse(it).atStartOfDay()
        }
        dateTo?.let {
            query.append(" AND a.appointmentDate <= :dateTo")
            params["dateTo"] = java.time.LocalDate.parse(it).plusDays(1).atStartOfDay()
        }
        searchTerm?.let {
            query.append(" AND (LOWER(a.title) LIKE :search OR LOWER(a.description) LIKE :search)")
            params["search"] = "%${it.lowercase()}%"
        }

        val jpql = query.toString()
        val q = em.createQuery(jpql, AppointmentEntity::class.java)
        params.forEach { (k, v) -> q.setParameter(k, v) }

        // Pagination
        q.firstResult = page * size
        q.maxResults = size

        val results = q.resultList

        // Count query for total
        val countQuery = StringBuilder(jpql.replace("SELECT a", "SELECT COUNT(a)"))
        val cq = em.createQuery(countQuery.toString())
        params.forEach { (k, v) -> cq.setParameter(k, v) }
        val total = (cq.singleResult as Long)

        return Pair(results, total)
    }

    fun unifiedSearch(
        searchTerm: String?,
        page: Int,
        size: Int
    ): Pair<List<Patient>, Long> {
        val query = StringBuilder("SELECT p FROM PatientEntity p WHERE 1=1")
        val params = mutableMapOf<String, Any>()
        searchTerm?.let {
            query.append(" AND (LOWER(p.firstName) LIKE :search OR LOWER(p.lastName) LIKE :search OR LOWER(p.mobileNumber) LIKE :search OR LOWER(p.email) LIKE :search)")
            params["search"] = "%${it.lowercase()}%"
        }

        val jpql = query.toString()
        val q = em.createQuery(jpql, Patient::class.java)
        params.forEach { (k, v) -> q.setParameter(k, v) }
        q.firstResult = page * size
        q.maxResults = size
        val results = q.resultList

        // Count
        val countQuery = jpql.replace("SELECT p", "SELECT COUNT(p)")
        val cq = em.createQuery(countQuery)
        params.forEach { (k, v) -> cq.setParameter(k, v) }
        val total = cq.singleResult as Long

        return Pair(results, total)
    }

    fun getAppointmentCounts(
        facilityId: String?,
        dateFrom: String?,
        dateTo: String?
    ): Map<String, Long> {
        val queryBuilder = StringBuilder("SELECT a.status, COUNT(a) FROM AppointmentEntity a WHERE a.status != 'Deleted'")
        if (facilityId != null) queryBuilder.append(" AND a.facilityId.hospitalId = :facilityId")
        if (dateFrom != null) queryBuilder.append(" AND a.appointmentDate >= :dateFrom")
        if (dateTo != null) queryBuilder.append(" AND a.appointmentDate <= :dateTo")
        queryBuilder.append(" GROUP BY a.status")

        val query = em.createQuery(queryBuilder.toString())
        if (facilityId != null) query.setParameter("facilityId", facilityId)
        if (dateFrom != null) query.setParameter("dateFrom", LocalDate.parse(dateFrom).atStartOfDay())
        if (dateTo != null) query.setParameter("dateTo", LocalDate.parse(dateTo).plusDays(1).atStartOfDay())

        val results = query.resultList as List<Array<Any>>
        return results.associate { (it[0] as String) to (it[1] as Long) }
    }

    fun getAppointmentsByType(
        facilityId: String?,
        dateFrom: String?,
        dateTo: String?
    ): Map<String, Long> {
        val queryBuilder = StringBuilder("SELECT a.type, COUNT(a) FROM AppointmentEntity a WHERE a.status != 'Deleted'")
        if (facilityId != null) queryBuilder.append(" AND a.facilityId.hospitalId = :facilityId")
        if (dateFrom != null) queryBuilder.append(" AND a.appointmentDate >= :dateFrom")
        if (dateTo != null) queryBuilder.append(" AND a.appointmentDate <= :dateTo")
        queryBuilder.append(" GROUP BY a.type")

        val query = em.createQuery(queryBuilder.toString())
        if (facilityId != null) query.setParameter("facilityId", facilityId)
        if (dateFrom != null) query.setParameter("dateFrom", LocalDate.parse(dateFrom).atStartOfDay())
        if (dateTo != null) query.setParameter("dateTo", LocalDate.parse(dateTo).plusDays(1).atStartOfDay())

        val results = query.resultList as List<Array<Any>>
        return results.associate { (it[0].toString()) to (it[1] as Long) }
    }

    fun getAppointmentsByPriority(
        facilityId: String?,
        dateFrom: String?,
        dateTo: String?
    ): Map<String, Long> {
        val queryBuilder = StringBuilder("SELECT a.priority, COUNT(a) FROM AppointmentEntity a WHERE a.status != 'Deleted'")
        if (facilityId != null) queryBuilder.append(" AND a.facilityId.hospitalId = :facilityId")
        if (dateFrom != null) queryBuilder.append(" AND a.appointmentDate >= :dateFrom")
        if (dateTo != null) queryBuilder.append(" AND a.appointmentDate <= :dateTo")
        queryBuilder.append(" GROUP BY a.priority")

        val query = em.createQuery(queryBuilder.toString())
        if (facilityId != null) query.setParameter("facilityId", facilityId)
        if (dateFrom != null) query.setParameter("dateFrom", LocalDate.parse(dateFrom).atStartOfDay())
        if (dateTo != null) query.setParameter("dateTo", LocalDate.parse(dateTo).plusDays(1).atStartOfDay())

        val results = query.resultList as List<Array<Any>>
        return results.associate { (it[0].toString()) to (it[1] as Long) }
    }

    // For today and upcoming appointments
    fun getTodayAndUpcomingCounts(
        facilityId: String?,
        dateFrom: String?,
        dateTo: String?
    ): Pair<Long, Long> {
        val today = LocalDate.now()
        val queryBuilder = StringBuilder("SELECT COUNT(a) FROM AppointmentEntity a WHERE a.status != 'Deleted'")
        if (facilityId != null) queryBuilder.append(" AND a.facilityId.hospitalId = :facilityId")
        if (dateFrom != null) queryBuilder.append(" AND a.appointmentDate >= :dateFrom")
        if (dateTo != null) queryBuilder.append(" AND a.appointmentDate <= :dateTo")
        val queryToday = em.createQuery("$queryBuilder AND a.appointmentDate = :today")
        val queryUpcoming = em.createQuery("$queryBuilder AND a.appointmentDate > :today")
        if (facilityId != null) {
            queryToday.setParameter("facilityId", facilityId)
            queryUpcoming.setParameter("facilityId", facilityId)
        }
        if (dateFrom != null) {
            val from = LocalDate.parse(dateFrom).atStartOfDay()
            queryToday.setParameter("dateFrom", from)
            queryUpcoming.setParameter("dateFrom", from)
        }
        if (dateTo != null) {
            val to = LocalDate.parse(dateTo).plusDays(1).atStartOfDay()
            queryToday.setParameter("dateTo", to)
            queryUpcoming.setParameter("dateTo", to)
        }
        queryToday.setParameter("today", today.atStartOfDay())
        queryUpcoming.setParameter("today", today.atStartOfDay())
        val todayCount = queryToday.singleResult as Long
        val upcomingCount = queryUpcoming.singleResult as Long
        return Pair(todayCount, upcomingCount)
    }

    fun findBriefAppointments(
        facilityId: String?,
        providerId: String?,
        dateFrom: LocalDate,
        dateTo: LocalDate
    ): List<AppointmentEntity> {
        val sb = StringBuilder("SELECT a FROM AppointmentEntity a WHERE a.status != 'Deleted'")
        if (facilityId != null) sb.append(" AND a.facilityId.hospitalId = :facilityId")
        if (providerId != null) sb.append(" AND a.providerId = :providerId")
        sb.append(" AND a.appointmentDate >= :dateFrom AND a.appointmentDate <= :dateTo")
        val query = em.createQuery(sb.toString(), AppointmentEntity::class.java)
        if (facilityId != null) query.setParameter("facilityId", facilityId)
        if (providerId != null) query.setParameter("providerId", providerId)
        query.setParameter("dateFrom", dateFrom.atStartOfDay())
        query.setParameter("dateTo", dateTo.atTime(23, 59, 59))
        return query.resultList
    }

    fun getQueueAppointments(
        serviceType: String,
        facilityId: String,
        date: LocalDate
    ): List<AppointmentEntity> {
        val appointmentTypeEnum = AppointmentType.fromValue(serviceType)
            ?: throw WebApplicationException(
                "Unknown serviceType '$serviceType'. Valid types: ${
                    AppointmentType.values().map { it.value }
                }", 400
            )

        val query = em.createQuery(
            """
        SELECT a FROM AppointmentEntity a
        WHERE a.type = :serviceType
        AND a.facilityId.hospitalId = :facilityId
        AND FUNCTION('DATE', a.appointmentDate) = :date
        AND a.status IN ('Waiting', 'Confirmed')
        ORDER BY a.startTime ASC
        """.trimIndent(), AppointmentEntity::class.java
        )
        query.setParameter("serviceType", appointmentTypeEnum)
        query.setParameter("facilityId", facilityId)
        query.setParameter("date", date)
        return query.resultList
    }


    @Transactional
    fun updateAppointmentPriorityAndFacility(
        appointmentId: String,
        facilityId: String,
        priority: String
    ): AppointmentEntity? {
        val entity = findById(appointmentId)
        if (entity != null) {
            entity.facilityId.hospitalId = facilityId
            entity.priority = priority
            // Optionally set status to "Waiting" if that's the business rule for queue
            entity.status = "Waiting"
            entity.updatedAt = java.time.LocalDateTime.now()
            persist(entity)
        }
        return entity
    }

    @Transactional
    fun updateQueueStatusAndNotes(
        appointmentId: String,
        newStatus: String,
        notes: String?
    ): AppointmentEntity? {
        val entity = findById(appointmentId)
        if (entity != null) {
            entity.status = newStatus
            if (notes != null) entity.notes = notes
            entity.updatedAt = java.time.LocalDateTime.now()
            persist(entity)
        }
        return entity
    }

    fun getAppointmentsInQueue(
        serviceType: String,
        facilityId: String
    ): List<AppointmentEntity> {
        val appointmentTypeEnum = sirobilt.meghasanjivini.common.enums.AppointmentType.fromValue(serviceType)
            ?: throw IllegalArgumentException("Invalid serviceType: $serviceType")

        val today = LocalDate.now()
        val query = em.createQuery(
            """
        SELECT a FROM AppointmentEntity a
        WHERE a.type = :serviceType
        AND a.facilityId.hospitalId = :facilityId
        AND FUNCTION('DATE', a.appointmentDate) = :date
        AND a.status IN ('Waiting', 'Called', 'InService')
        ORDER BY 
            CASE a.priority
                WHEN 'Emergency' THEN 1
                WHEN 'Urgent' THEN 2
                WHEN 'High' THEN 3
                WHEN 'Normal' THEN 4
                WHEN 'Low' THEN 5
                ELSE 6
            END,
            a.startTime ASC
        """.trimIndent(), AppointmentEntity::class.java
        )
        query.setParameter("serviceType", appointmentTypeEnum)
        query.setParameter("facilityId", facilityId)
        query.setParameter("date", today)
        return query.resultList
    }

    // Completed appointments for stats
    fun findCompleted(
        facilityId: String,
        date: LocalDate,
        serviceType: String? = null
    ): List<AppointmentEntity> {
        val sb = StringBuilder(
            "SELECT a FROM AppointmentEntity a " +
                    "WHERE a.facilityId.hospitalId = :facilityId " +
                    "AND FUNCTION('DATE', a.appointmentDate) = :date " +
                    "AND a.status = 'Completed'"
        )
        if (serviceType != null) sb.append(" AND a.type = :serviceType")
        sb.append(" ORDER BY a.startTime ASC")

        val q = em.createQuery(sb.toString(), AppointmentEntity::class.java)
        q.setParameter("facilityId", facilityId)
        q.setParameter("date", date)
        if (serviceType != null) {
            q.setParameter("serviceType",
                AppointmentType.Companion.fromValue(serviceType))
        }
        return q.resultList
    }

    // Count current in-queue
    fun countInQueue(
        facilityId: String,
        date: LocalDate,
        serviceType: String? = null
    ): Long {
        val sb = StringBuilder(
            "SELECT COUNT(a) FROM AppointmentEntity a " +
                    "WHERE a.facilityId.hospitalId = :facilityId " +
                    "AND FUNCTION('DATE', a.appointmentDate) = :date " +
                    "AND a.status IN ('Waiting','Confirmed')"
        )
        if (serviceType != null) sb.append(" AND a.type = :serviceType")

        val q = em.createQuery(sb.toString(), java.lang.Long::class.java)
        q.setParameter("facilityId", facilityId)
        q.setParameter("date", date)
        if (serviceType != null) {
            q.setParameter("serviceType",
                AppointmentType.Companion.fromValue(serviceType))
        }
        return q.singleResult.toLong()
    }

    @Transactional
    fun deleteQueueAppointment(
        appointmentId: String,
        reason: String,
        notes: String?
    ): AppointmentEntity {
        val entity = findById(appointmentId)
            ?: throw WebApplicationException("Appointment $appointmentId not found", 404)

        entity.status    = "Deleted"
        entity.reason    = reason
        if (notes != null) entity.notes = notes
        entity.updatedAt = LocalDateTime.now()

        // persist(entity) isn’t strictly needed if entity is already managed,
        // but we call it to be explicit:
        persist(entity)

        return entity
    }

}
