package sirobilt.meghasanjivini.appointment.dto


import java.time.LocalDateTime


data class AppointmentDTO(
    val id: String? = null,
    val patientId: String,
    val providerId: String,
    val facilityId: String,
    val appointmentDate: LocalDateTime,
    val startTime: LocalDateTime,
    val endTime: LocalDateTime,
    val duration: Int,
    val type: String,
    val status: String = "Scheduled",
    val priority: String = "Normal",
    val title: String? = null,
    val description: String? = null,
    val notes: String? = null,
    val reason: String? = null,
    val isRecurring: Boolean = false,
    val recurringPattern: String? = null,
    val recurringEndDate: String? = null,
    val parentAppointmentId: String? = null,
    val externalSystemId: String? = null,
    val externalSystemName: String? = null,
    val createdBy: String,
    val updatedBy: String,
    val slotNumber: Long
)