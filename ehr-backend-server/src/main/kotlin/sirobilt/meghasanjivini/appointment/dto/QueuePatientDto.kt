package sirobilt.meghasanjivini.appointment.dto

data class QueuePatientDto(
    val appointmentId: String,
    val patientId: String,
    val patientName: String,
    val queueNumber: Int,
    val estimatedWaitTime: Int,
    val estimatedServiceTime: String,
    val priority: String,
    val status: String
)

data class CurrentServingDto(
    val appointmentId: String,
    val patientName: String,
    val queueNumber: Int,
    val estimatedServiceTime: String
)

data class QueueStatusDto(
    val serviceType: String,
    val facilityId: String,
    val date: String,
    val totalInQueue: Int,
    val currentlyServing: CurrentServingDto?,
    val queue: List<QueuePatientDto>,
    val averageServiceTime: Int,
    val estimatedWaitTime: Int
)

data class AddToQueueRequest(
    val appointmentId: String,
    val facilityId: String,
    val priority: String // "Low", "Normal", "High", "Urgent", "Emergency"
)