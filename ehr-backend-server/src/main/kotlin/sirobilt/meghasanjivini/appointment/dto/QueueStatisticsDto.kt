package sirobilt.meghasanjivini.appointment.dto

data class QueueStatisticsDto(
    val facilityId: String,
    val date: String,
    val totalProcessed: Int,
    val currentInQueue: Int,
    val averageWaitTime: Int,
    val averageServiceTime: Int,
    val peakHours: List<String>,
    val serviceStats: Map<String, ServiceStatDto>,
    val hourlyStats: List<HourlyStatDto>
)

data class ServiceStatDto(
    val totalProcessed: Int,
    val averageWaitTime: Int,
    val averageServiceTime: Int
)

data class HourlyStatDto(
    val hour: String,
    val patientsServed: Int,
    val averageWaitTime: Int
)