package sirobilt.meghasanjivini.appointment.dto

import java.time.LocalDate
import java.time.LocalTime
import java.time.OffsetDateTime

data class AppointmentResponseDTO(
    val appointmentId: String,
    val patientId: String,
    val providerId: String,
    val facilityId: String,
    val appointmentDate: LocalDate,
    val startTime: LocalTime,
    val endTime: LocalTime,
    val duration: Int,
    val type: String,
    val status: String,
    val priority: String,
    val title: String?,
    val description: String?,
    val notes: String?,
    val reason: String?,
    val isRecurring: Boolean,
    val recurringPattern: String?,
    val recurringEndDate: String?,
    val parentAppointmentId: String?,
    val createdAt: OffsetDateTime,
    val updatedAt: OffsetDateTime,
    val createdBy: String?,
    val updatedBy: String?,
    val patient: PatientInfoDTO,
    val provider: ProviderInfoDTO,
    val facility: FacilityInfoDTO
)

data class PatientInfoDTO(
    val firstName: String?,
    val lastName: String?,

)

data class ProviderInfoDTO(
    val firstName: String,
    val lastName: String,
    val fullName: String

)

data class FacilityInfoDTO(
    val facilityName: String,
    val address: String
)