package sirobilt.meghasanjivini.appointment.dto

data class AppointmentStatisticsDto(
    val totalAppointments: Long,
    val scheduledAppointments: Long,
    val confirmedAppointments: Long,
    val completedAppointments: Long,
    val cancelledAppointments: Long,
    val noShowAppointments: Long,
    val todayAppointments: Long,
    val upcomingAppointments: Long,
    val appointmentsByType: Map<String, Long>,
    val appointmentsByPriority: Map<String, Long>,
    val averageWaitTime: Double,         // dummy value for now
    val patientSatisfactionScore: Double // dummy value for now
)