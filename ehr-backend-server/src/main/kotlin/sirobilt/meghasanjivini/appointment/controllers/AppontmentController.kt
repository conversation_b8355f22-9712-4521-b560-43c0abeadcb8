package sirobilt.meghasanjivini.appointment.controllers

import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jakarta.inject.Inject
import org.acme.appointment.dto.RescheduleAppointmentDTO
import org.eclipse.microprofile.openapi.annotations.Operation
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter
import org.eclipse.microprofile.openapi.annotations.tags.Tag
import sirobilt.meghasanjivini.appointment.AppointmentService
import sirobilt.meghasanjivini.appointment.dto.*


@Path("/appointments")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Appointments", description = "API for managing appointments")
class AppointmentController @Inject constructor(
    private val service: AppointmentService
) {

    @GET
    @Operation(summary = "List all appointments")
    fun list(): List<AppointmentDTO> = service.findAll()

    @GET
    @Path("/{id}")
    @Operation(summary = "Get appointment by ID")
    fun get(@PathParam("id") id: String): ApiResponse<AppointmentDTO> {
        val result = service.findById(id)
        return ApiResponse(success = true, data = result)
    }

    @POST
    @Operation(summary = "Create an appointment")
    fun create(dto: AppointmentDTO?): ApiResponse<AppointmentResponseDTO> {
        if (dto == null) throw WebApplicationException("Request body is required", Response.Status.BAD_REQUEST)
        val result = service.create(dto, changedBy = "admin")
        return ApiResponse(success = true, data = result)
    }

    @PUT
    @Path("/{id}")
    @Operation(summary = "Update an existing appointment")
    fun update(@PathParam("id") id: String, dto: AppointmentDTO?): AppointmentDTO {
        if (dto == null) throw WebApplicationException("Request body is required", Response.Status.BAD_REQUEST)
        return service.update(id, dto, changedBy = "admin")
    }

    @DELETE
    @Path("/{id}")
    @Operation(summary = "Delete an appointment by ID")
    fun delete(@PathParam("id") id: String): Response {
        service.delete(id)
        return Response.noContent().build()
    }

    @GET
    @Path("/query")
    @Operation(summary = "Query appointments with filters")
    fun getAppointments(
        @Parameter(description = "Patient ID") @QueryParam("patientId") patientId: String?,
        @Parameter(description = "Provider ID") @QueryParam("providerId") providerId: String?,
        @Parameter(description = "Facility ID") @QueryParam("facilityId") facilityId: String?,
        @Parameter(description = "Status filter") @QueryParam("status") status: List<String>?,
        @Parameter(description = "Type filter") @QueryParam("type") type: List<String>?,
        @Parameter(description = "Priority filter") @QueryParam("priority") priority: List<String>?,
        @Parameter(description = "Date from") @QueryParam("dateFrom") dateFrom: String?,
        @Parameter(description = "Date to") @QueryParam("dateTo") dateTo: String?,
        @Parameter(description = "Search term") @QueryParam("searchTerm") searchTerm: String?,
        @Parameter(description = "Page number") @QueryParam("page") page: Int?,
        @Parameter(description = "Page size") @QueryParam("size") size: Int?
    ): PaginatedResponse<Map<String, List<AppointmentResponseDTO>>> =
        service.getAppointmentsWithFilters(patientId, providerId, facilityId, status, type, priority, dateFrom, dateTo, searchTerm, page ?: 0, size ?: 20)

    @PATCH
    @Path("/{appointmentId}/cancel")
    @Operation(summary = "Cancel appointment")
    fun cancelAppointment(@PathParam("appointmentId") appointmentId: String, dto: CancelAppointmentDto): ApiResponse<AppointmentResponseDTO> =
        ApiResponse(true, service.cancelAppointment(appointmentId, dto))

    @PATCH
    @Path("/{appointmentId}/confirm")
    @Operation(summary = "Confirm appointment")
    fun confirmAppointment(@PathParam("appointmentId") appointmentId: String, dto: ConfirmAppointmentDto): ApiResponse<AppointmentResponseDTO> =
        ApiResponse(true, service.confirmAppointment(appointmentId, dto))

    @PATCH
    @Path("/{appointmentId}/reschedule")
    @Operation(summary = "Reschedule appointment")
    fun rescheduleAppointment(@PathParam("appointmentId") appointmentId: String, dto: RescheduleAppointmentDTO): ApiResponse<AppointmentResponseDTO> =
        ApiResponse(true, service.rescheduleAppointment(appointmentId, dto))

    @GET
    @Path("/stats")
    @Operation(summary = "Get appointment statistics")
    fun getAppointmentStatistics(@QueryParam("facilityId") facilityId: String?, @QueryParam("dateFrom") dateFrom: String?, @QueryParam("dateTo") dateTo: String?): ApiResponse<AppointmentStatisticsDto> =
        ApiResponse(true, service.getAppointmentStatistics(facilityId, dateFrom, dateTo))

    @GET
    @Path("/today")
    @Operation(summary = "Get today's appointments")
    fun getTodaysAppointments(@QueryParam("facilityId") facilityId: String?, @QueryParam("providerId") providerId: String?): ApiResponse<List<AppointmentBriefDTO>> =
        ApiResponse(true, service.getTodaysAppointmentsBrief(facilityId, providerId))

    @GET
    @Path("/upcoming")
    @Operation(summary = "Get upcoming appointments")
    fun getUpcomingAppointments(@QueryParam("facilityId") facilityId: String?, @QueryParam("providerId") providerId: String?, @QueryParam("days") days: Int?): ApiResponse<List<AppointmentBriefDTO>> =
        ApiResponse(true, service.getUpcomingAppointmentsBrief(facilityId, providerId, days ?: 7))


}
