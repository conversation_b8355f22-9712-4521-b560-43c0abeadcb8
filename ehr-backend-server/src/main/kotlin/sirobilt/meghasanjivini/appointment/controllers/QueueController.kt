package sirobilt.meghasanjivini.appointment.controllers

import jakarta.ws.rs.*
import jakarta.ws.rs.core.Response
import jakarta.inject.Inject
import jakarta.validation.Valid
import sirobilt.meghasanjivini.appointment.AppointmentService

import sirobilt.meghasanjivini.appointment.dto.*

import java.time.LocalDate

@Path("/queue")
class QueueController @Inject constructor(
    private val appointmentService: AppointmentService
) {
    @GET
    @Path("/{serviceType}")
    fun getQueue(
        @PathParam("serviceType") serviceType: String,
        @QueryParam("facilityId") facilityId: String,
        @QueryParam("date") date: String?
    ): ApiResponse<QueueStatusDto> {
        if (facilityId.isNullOrBlank()) {
            throw WebApplicationException("facilityId is required", Response.Status.BAD_REQUEST)
        }
        val queryDate = date?.let { LocalDate.parse(it) } ?: LocalDate.now()
        val dto = appointmentService.getServiceQueue(serviceType, facilityId, queryDate)
        return ApiResponse(true, dto)
    }

    @POST
    @Path("/{serviceType}/add")
    fun addToQueue(
        @PathParam("serviceType") serviceType: String,
        dto: AddToQueueRequestDto
    ): ApiResponse<AppointmentResponseDTO> {
        val result = appointmentService.addToQueue(serviceType, dto)
        return ApiResponse(true, result)
    }

    @PATCH
    @Path("/{serviceType}/{appointmentId}")
    fun updateQueueStatus(
        @PathParam("serviceType") serviceType: String,
        @PathParam("appointmentId") appointmentId: String,
        dto: UpdateQueueStatusDto
    ): ApiResponse<AppointmentResponseDTO> {
        val result = appointmentService.updateQueueStatus(serviceType, appointmentId, dto)
        return ApiResponse(true, result)
    }

    @GET
    @Path("/{serviceType}/wait-time")
    fun getWaitTimeEstimation(
        @PathParam("serviceType") serviceType: String,
        @QueryParam("facilityId") facilityId: String,
        @QueryParam("priority") priority: String?
    ): ApiResponse<WaitTimeEstimationDto> {
        if (facilityId.isNullOrBlank()) {
            throw WebApplicationException("facilityId is required", 400)
        }
        val result = appointmentService.getWaitTimeEstimation(serviceType, facilityId, priority)
        return ApiResponse(true, result)
    }

    @GET
    @Path("/stats")
    fun getQueueStatistics(
        @QueryParam("facilityId") facilityId: String,
        @QueryParam("serviceType") serviceType: String?,
        @QueryParam("date") date: String?
    ): ApiResponse<QueueStatisticsDto> {
        if (facilityId.isBlank()) {
            throw WebApplicationException("facilityId is required", Response.Status.BAD_REQUEST)
        }
        val dt = date?.let { LocalDate.parse(it) } ?: LocalDate.now()
        val result = appointmentService.getQueueStatistics(facilityId, serviceType, dt)
        return ApiResponse(true, result)
    }

    @DELETE
    @Path("{serviceType}/{appointmentId}")
    fun deleteFromQueue(
        @PathParam("serviceType") serviceType: String,
        @PathParam("appointmentId") appointmentId: String,
        @Valid deleteReq: DeleteAppointmentDto
    ) {
        appointmentService.removeFromQueue(serviceType, appointmentId, deleteReq)
    }

}