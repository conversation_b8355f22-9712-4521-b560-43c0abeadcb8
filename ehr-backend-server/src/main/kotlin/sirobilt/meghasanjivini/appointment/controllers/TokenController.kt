package sirobilt.meghasanjivini.appointment.controllers

import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import sirobilt.meghasanjivini.appointment.model.DepartmentToken
import sirobilt.meghasanjivini.appointment.repository.TokenRepository
import sirobilt.meghasanjivini.appointment.service.TokenService
import sirobilt.meghasanjivini.appointment.util.QRCodeUtil
import java.time.LocalDate
import java.util.*

@Path("/tokens")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
class TokenController @Inject constructor(
    private val tokenService: TokenService,
    private val tokenRepository: TokenRepository
) {



    data class TokenResponse(
        val patientId: String,
        val name: String,
        val mobile: String,
        val tokenNumber: Int,
        val specializationId: UUID,
        val tokenDate: LocalDate,
        val timestamp: String,
        val qrCodeBase64: String
    )

    @POST
    @Path("/generate")
    fun generateEnhancedToken(
        @QueryParam("patientId") patientId: String,
        @QueryParam("specializationId") specializationId: UUID?,
        @QueryParam("name") name: String,
        @QueryParam("mobile") mobile: String
    ): TokenResponse {
        val token = if (specializationId != null)
            tokenService.generateToken(patientId, specializationId)
        else
            tokenService.generateTokenForGeneral(patientId)

        val qrUrl = "https://megha-dev.sirobilt.com/patient/${token.patientId}"
        val qrCodeBase64 = QRCodeUtil.generateQRCodeBase64(qrUrl)

        val nowInKolkata = java.time.ZonedDateTime.now(java.time.ZoneId.of("Asia/Kolkata"))
            .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm"))

        return TokenResponse(
            patientId = patientId,
            name = name,
            mobile = mobile,
            tokenNumber = token.tokenNumber,
            specializationId = token.specializationId,
            tokenDate = token.tokenDate,
            timestamp = nowInKolkata,
            qrCodeBase64 = qrCodeBase64
        )
    }


    @GET
    fun listTokens(@QueryParam("specializationId") specializationId: UUID?): List<TokenResponses> {
        val tokens: List<DepartmentToken> = if (specializationId != null) {
            tokenRepository.find(
                "specializationId = ?1",
                specializationId
            ).list()
        } else {
            tokenRepository.listAll()
        }

        return tokens.map { token ->
            TokenResponses(
                tokenId = token.tokenId!!,
                patientId = token.patientId,
                specializationId = token.specializationId,
                tokenNumber = token.tokenNumber,
                tokenDate = token.tokenDate,
                createdAt = token.createdAt
            )
        }
    }

    data class TokenResponses(
        val tokenId: UUID,
        val patientId: String,
        val specializationId: UUID,
        val tokenNumber: Int,
        val tokenDate: LocalDate,
        val createdAt: LocalDate
    )
}
