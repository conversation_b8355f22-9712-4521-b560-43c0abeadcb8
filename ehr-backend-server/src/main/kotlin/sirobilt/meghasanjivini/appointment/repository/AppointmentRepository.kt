package sirobilt.meghasanjivini.appointment.repository

import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepositoryBase
import jakarta.enterprise.context.ApplicationScoped
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import sirobilt.meghasanjivini.appointment.AppointmentEntity
import sirobilt.meghasanjivini.patientregistration.model.Patient
import java.time.LocalDate

@ApplicationScoped
class AppointmentRepository: PanacheRepositoryBase<AppointmentEntity, String> {
    @PersistenceContext
    lateinit var em: EntityManager


    fun getQueueAppointments(
        serviceType: String,
        facilityId: String,
        date: LocalDate
    ): List<AppointmentEntity> {
        val query = em.createQuery(
            """
        SELECT a FROM AppointmentEntity a
        WHERE a.type = :serviceType
        AND a.facilityId.hospitalId = :facilityId
        AND DATE(a.appointmentDate) = :date
        AND a.status IN ('Waiting', 'Confirmed') -- or your relevant queue statuses
        ORDER BY a.startTime ASC
        """.trimIndent(), AppointmentEntity::class.java
        )
        query.setParameter("serviceType", serviceType)
        query.setParameter("facilityId", facilityId)
        query.setParameter("date", date)
        return query.resultList
    }

    fun searchAppointments(
        patientId: String?,
        providerId: String?,
        facilityId: String?,
        status: List<String>?,
        type: List<String>?,
        priority: List<String>?,
        dateFrom: String?,
        dateTo: String?,
        searchTerm: String?,
        page: Int,
        size: Int
    ): Pair<List<AppointmentEntity>, Long> {
        val query = StringBuilder("SELECT a FROM AppointmentEntity a WHERE 1=1")
        val params = mutableMapOf<String, Any>()

        patientId?.let {
            query.append(" AND a.patient.upId = :patientId")
            params["patientId"] = it
        }
        providerId?.let {
            query.append(" AND a.providerId = :providerId")
            params["providerId"] = it
        }
        facilityId?.let {
            query.append(" AND a.facilityId.hospitalId = :facilityId")
            params["facilityId"] = it
        }
        status?.takeIf { it.isNotEmpty() }?.let {
            query.append(" AND a.status IN :status")
            params["status"] = it
        }
        type?.takeIf { it.isNotEmpty() }?.let {
            query.append(" AND a.type IN :type")
            params["type"] = it
        }
        priority?.takeIf { it.isNotEmpty() }?.let {
            query.append(" AND a.priority IN :priority")
            params["priority"] = it
        }
        dateFrom?.let {
            query.append(" AND a.appointmentDate >= :dateFrom")
            params["dateFrom"] = LocalDate.parse(it).atStartOfDay()
        }
        dateTo?.let {
            query.append(" AND a.appointmentDate <= :dateTo")
            params["dateTo"] = LocalDate.parse(it).plusDays(1).atStartOfDay()
        }
        searchTerm?.let {
            query.append(" AND (LOWER(a.title) LIKE :search OR LOWER(a.description) LIKE :search)")
            params["search"] = "%${it.lowercase()}%"
        }

        val jpql = query.toString()
        val q = em.createQuery(jpql, AppointmentEntity::class.java)
        params.forEach { (k, v) -> q.setParameter(k, v) }

        // Pagination
        q.firstResult = page * size
        q.maxResults = size

        val results = q.resultList

        // Count query for total
        val countQuery = StringBuilder(jpql.replace("SELECT a", "SELECT COUNT(a)"))
        val cq = em.createQuery(countQuery.toString())
        params.forEach { (k, v) -> cq.setParameter(k, v) }
        val total = (cq.singleResult as Long)

        return Pair(results, total)
    }

    fun unifiedSearch(
        searchTerm: String?,
        page: Int,
        size: Int
    ): Pair<List<Patient>, Long> {
        val query = StringBuilder("SELECT p FROM PatientEntity p WHERE 1=1")
        val params = mutableMapOf<String, Any>()
        searchTerm?.let {
            query.append(" AND (LOWER(p.firstName) LIKE :search OR LOWER(p.lastName) LIKE :search OR LOWER(p.mobileNumber) LIKE :search OR LOWER(p.email) LIKE :search)")
            params["search"] = "%${it.lowercase()}%"
        }

        val jpql = query.toString()
        val q = em.createQuery(jpql, Patient::class.java)
        params.forEach { (k, v) -> q.setParameter(k, v) }
        q.firstResult = page * size
        q.maxResults = size
        val results = q.resultList

        // Count
        val countQuery = jpql.replace("SELECT p", "SELECT COUNT(p)")
        val cq = em.createQuery(countQuery)
        params.forEach { (k, v) -> cq.setParameter(k, v) }
        val total = cq.singleResult as Long

        return Pair(results, total)
    }

    fun getAppointmentCounts(
        facilityId: String?,
        dateFrom: String?,
        dateTo: String?
    ): Map<String, Long> {
        val queryBuilder = StringBuilder("SELECT a.status, COUNT(a) FROM AppointmentEntity a WHERE a.status != 'Deleted'")
        if (facilityId != null) queryBuilder.append(" AND a.facilityId.hospitalId = :facilityId")
        if (dateFrom != null) queryBuilder.append(" AND a.appointmentDate >= :dateFrom")
        if (dateTo != null) queryBuilder.append(" AND a.appointmentDate <= :dateTo")
        queryBuilder.append(" GROUP BY a.status")

        val query = em.createQuery(queryBuilder.toString())
        if (facilityId != null) query.setParameter("facilityId", facilityId)
        if (dateFrom != null) query.setParameter("dateFrom", LocalDate.parse(dateFrom).atStartOfDay())
        if (dateTo != null) query.setParameter("dateTo", LocalDate.parse(dateTo).plusDays(1).atStartOfDay())

        val results = query.resultList as List<Array<Any>>
        return results.associate { (it[0] as String) to (it[1] as Long) }
    }

    fun getAppointmentsByType(
        facilityId: String?,
        dateFrom: String?,
        dateTo: String?
    ): Map<String, Long> {
        val queryBuilder = StringBuilder("SELECT a.type, COUNT(a) FROM AppointmentEntity a WHERE a.status != 'Deleted'")
        if (facilityId != null) queryBuilder.append(" AND a.facilityId.hospitalId = :facilityId")
        if (dateFrom != null) queryBuilder.append(" AND a.appointmentDate >= :dateFrom")
        if (dateTo != null) queryBuilder.append(" AND a.appointmentDate <= :dateTo")
        queryBuilder.append(" GROUP BY a.type")

        val query = em.createQuery(queryBuilder.toString())
        if (facilityId != null) query.setParameter("facilityId", facilityId)
        if (dateFrom != null) query.setParameter("dateFrom", LocalDate.parse(dateFrom).atStartOfDay())
        if (dateTo != null) query.setParameter("dateTo", LocalDate.parse(dateTo).plusDays(1).atStartOfDay())

        val results = query.resultList as List<Array<Any>>
        return results.associate { (it[0].toString()) to (it[1] as Long) }
    }

    fun getAppointmentsByPriority(
        facilityId: String?,
        dateFrom: String?,
        dateTo: String?
    ): Map<String, Long> {
        val queryBuilder = StringBuilder("SELECT a.priority, COUNT(a) FROM AppointmentEntity a WHERE a.status != 'Deleted'")
        if (facilityId != null) queryBuilder.append(" AND a.facilityId.hospitalId = :facilityId")
        if (dateFrom != null) queryBuilder.append(" AND a.appointmentDate >= :dateFrom")
        if (dateTo != null) queryBuilder.append(" AND a.appointmentDate <= :dateTo")
        queryBuilder.append(" GROUP BY a.priority")

        val query = em.createQuery(queryBuilder.toString())
        if (facilityId != null) query.setParameter("facilityId", facilityId)
        if (dateFrom != null) query.setParameter("dateFrom", LocalDate.parse(dateFrom).atStartOfDay())
        if (dateTo != null) query.setParameter("dateTo", LocalDate.parse(dateTo).plusDays(1).atStartOfDay())

        val results = query.resultList as List<Array<Any>>
        return results.associate { (it[0].toString()) to (it[1] as Long) }
    }

    // For today and upcoming appointments
    fun getTodayAndUpcomingCounts(
        facilityId: String?,
        dateFrom: String?,
        dateTo: String?
    ): Pair<Long, Long> {
        val today = LocalDate.now()
        val queryBuilder = StringBuilder("SELECT COUNT(a) FROM AppointmentEntity a WHERE a.status != 'Deleted'")
        if (facilityId != null) queryBuilder.append(" AND a.facilityId.hospitalId = :facilityId")
        if (dateFrom != null) queryBuilder.append(" AND a.appointmentDate >= :dateFrom")
        if (dateTo != null) queryBuilder.append(" AND a.appointmentDate <= :dateTo")
        val queryToday = em.createQuery("$queryBuilder AND a.appointmentDate = :today")
        val queryUpcoming = em.createQuery("$queryBuilder AND a.appointmentDate > :today")
        if (facilityId != null) {
            queryToday.setParameter("facilityId", facilityId)
            queryUpcoming.setParameter("facilityId", facilityId)
        }
        if (dateFrom != null) {
            val from = LocalDate.parse(dateFrom).atStartOfDay()
            queryToday.setParameter("dateFrom", from)
            queryUpcoming.setParameter("dateFrom", from)
        }
        if (dateTo != null) {
            val to = LocalDate.parse(dateTo).plusDays(1).atStartOfDay()
            queryToday.setParameter("dateTo", to)
            queryUpcoming.setParameter("dateTo", to)
        }
        queryToday.setParameter("today", today.atStartOfDay())
        queryUpcoming.setParameter("today", today.atStartOfDay())
        val todayCount = queryToday.singleResult as Long
        val upcomingCount = queryUpcoming.singleResult as Long
        return Pair(todayCount, upcomingCount)
    }

    fun findBriefAppointments(
        facilityId: String?,
        providerId: String?,
        dateFrom: LocalDate,
        dateTo: LocalDate
    ): List<AppointmentEntity> {
        val sb = StringBuilder("SELECT a FROM AppointmentEntity a WHERE a.status != 'Deleted'")
        if (facilityId != null) sb.append(" AND a.facilityId.hospitalId = :facilityId")
        if (providerId != null) sb.append(" AND a.providerId = :providerId")
        sb.append(" AND a.appointmentDate >= :dateFrom AND a.appointmentDate <= :dateTo")
        val query = em.createQuery(sb.toString(), AppointmentEntity::class.java)
        if (facilityId != null) query.setParameter("facilityId", facilityId)
        if (providerId != null) query.setParameter("providerId", providerId)
        query.setParameter("dateFrom", dateFrom.atStartOfDay())
        query.setParameter("dateTo", dateTo.atTime(23, 59, 59))
        return query.resultList
    }

}