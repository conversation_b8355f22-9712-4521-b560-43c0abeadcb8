package sirobilt.meghasanjivini.appointment.repository

import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepositoryBase
import jakarta.enterprise.context.ApplicationScoped
import sirobilt.meghasanjivini.appointment.model.DepartmentToken
import java.time.LocalDate
import java.util.*

@ApplicationScoped
class TokenRepository : PanacheRepositoryBase<DepartmentToken, UUID> {

    fun findMaxTokenNumber(specializationId: UUID, date: LocalDate): Int? {
        return find(
            "SELECT MAX(t.tokenNumber) FROM DepartmentToken t WHERE t.specializationId = ?1 AND t.tokenDate = ?2",
            specializationId, date
        ).firstResult() as? Int
    }
}
