package sirobilt.meghasanjivini.appointment.model

import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.*
import java.time.LocalDate
import java.util.*

@Entity
@Table(
    name = "department_tokens",
    uniqueConstraints = [
        UniqueConstraint(columnNames = ["specialization_id", "token_date", "token_number"]),
        UniqueConstraint(columnNames = ["specialization_id", "token_date", "patient_id"])]
)
class DepartmentToken : PanacheEntityBase {

    @Id
    @Column(name = "token_id", columnDefinition = "UUID")
    var tokenId: UUID? = null

    @Column(name = "patient_id", nullable = false)
    lateinit var patientId: String

    @Column(name = "specialization_id", nullable = false, columnDefinition = "UUID")
    lateinit var specializationId: UUID

    @Column(name = "token_number", nullable = false)
    var tokenNumber: Int = 0

    @Column(name = "token_date", nullable = false)
    lateinit var tokenDate: LocalDate

    @Column(name = "created_at", nullable = false)
    var createdAt: LocalDate = LocalDate.now()
}
