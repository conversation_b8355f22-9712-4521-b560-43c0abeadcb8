package sirobilt.meghasanjivini.appointment.service

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import sirobilt.meghasanjivini.appointment.model.DepartmentToken
import sirobilt.meghasanjivini.appointment.repository.TokenRepository
import sirobilt.meghasanjivini.masterdata.repository.SpecializationRepository
import java.time.LocalDate
import java.util.*

@ApplicationScoped
class TokenService @Inject constructor(
    private val tokenRepository: TokenRepository,
    private val specializationRepository: SpecializationRepository
) {

    fun getDefaultSpecialization(): UUID {
        return specializationRepository.findByCode("GENERAL")
            ?.specializationId
            ?: throw IllegalStateException("Default specialization 'GENERAL' not found in database")
    }

    @Transactional
    fun generateToken(patientId: String, specializationId: UUID): DepartmentToken {
        val today = LocalDate.now()

        val existingToken = tokenRepository.find(
            "patientId = ?1 AND specializationId = ?2 AND tokenDate = ?3",
            patientId, specializationId, today
        ).firstResult()

        if (existingToken != null) {
            return existingToken
        }

        val latestToken = tokenRepository.findMaxTokenNumber(specializationId, today) ?: 0

        val token = DepartmentToken().apply {
            tokenId = UUID.randomUUID()
            this.patientId = patientId
            this.specializationId = specializationId
            this.tokenNumber = latestToken + 1
            this.tokenDate = today
            this.createdAt = LocalDate.now()
        }

        tokenRepository.persist(token)
        return token
    }

    @Transactional
    fun generateTokenForGeneral(patientId: String): DepartmentToken {
        return generateToken(patientId, getDefaultSpecialization())
    }
}
