package sirobilt.meghasanjivini.appointmentconfig

import sirobilt.meghasanjivini.common.enums.SlotAvailability
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID

class AppointmentSlotDto() {

    lateinit var consultantId: UUID
    var slotDate: LocalDate? = null
    var startTime: LocalTime? = null
    var endTime: LocalTime? = null
    var slotNumber: Int? = null
    var availability: SlotAvailability? = null
}