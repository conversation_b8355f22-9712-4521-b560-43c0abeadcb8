package sirobilt.meghasanjivini.appointmentconfig

import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID

class ConsultantSlotConfigDto() {

    lateinit var consultantId: UUID
    var daysOfWeek: List<DayOfWeek> = emptyList()
    var startTime: LocalTime? = null
    var endTime: LocalTime? = null
    var slotDuration: Int = 0
    var effectiveFrom: LocalDate? = null
    var effectiveTo: LocalDate? = null
}