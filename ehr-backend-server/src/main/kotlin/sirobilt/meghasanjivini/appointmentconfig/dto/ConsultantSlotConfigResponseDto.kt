package sirobilt.meghasanjivini.appointmentconfig.dto

import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID

data class ConsultantSlotConfigResponseDto(
    var scheduleId: Long,
    var consultantId: UUID,
    var daysOfWeek: List<DayOfWeek>,
    var startTime: LocalTime,
    var endTime: LocalTime,
    var slotDuration: Int,
    var effectiveFrom: LocalDate,
    var effectiveTo: LocalDate?
)
