package sirobilt.meghasanjivini.appointmentconfig

import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.*
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID

@Entity
@Table(name = "consultant_slot_config")
class ConsultantSlotConfig : PanacheEntityBase {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var scheduleId: Long? = null  // ✅ MUST be present

    @Column(name = "consultant_id", nullable = false)
    lateinit var consultantId: UUID

    @ElementCollection(targetClass = DayOfWeek::class, fetch = FetchType.EAGER)
    @CollectionTable(
        name = "consultant_slot_config_day",
        joinColumns = [JoinColumn(name = "config_id")]
    )
    @Column(name = "day_of_week", nullable = false, length = 10)
    @Enumerated(EnumType.STRING)
    var daysOfWeek: List<DayOfWeek> = mutableListOf()

    @Column(name = "start_time", nullable = false)
    var startTime: LocalTime = LocalTime.MIN

    @Column(name = "end_time", nullable = false)
    var endTime: LocalTime = LocalTime.MIN

    @Column(name = "slot_duration", nullable = false)
    var slotDuration: Int = 15

    @Column(name = "effective_from", nullable = false)
    var effectiveFrom: LocalDate = LocalDate.now()

    @Column(name = "effective_to")
    var effectiveTo: LocalDate? = null
}
