package org.acme.appointmentconfig.repositories


import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepositoryBase
import jakarta.enterprise.context.ApplicationScoped

import sirobilt.meghasanjivini.appointmentconfig.AppointmentSlot
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@ApplicationScoped
class AppointmentSlotRepo : PanacheRepositoryBase<AppointmentSlot, Long> {
    fun findOpenSlots(
        consId: Long, date: LocalDate
    ): List<AppointmentSlot> = find(
        "consultantId = ?1 and slotDate = ?2 and availability = 'OPEN'",
        consId, date
    )
        .list()

    fun findSlotByConsultantDateAndNumber(
        consultantId: UUID,
        date: LocalDateTime,
        slotNumber: Long
    ): AppointmentSlot? = find(
        "consultantId = ?1 and slotDate = ?2 and slotNumber = ?3",
        consultantId, date, slotNumber
    ).firstResult()
}

