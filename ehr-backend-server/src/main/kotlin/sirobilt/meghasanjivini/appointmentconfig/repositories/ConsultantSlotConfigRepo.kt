package sirobilt.meghasanjivini.appointmentconfig


import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepositoryBase
import jakarta.enterprise.context.ApplicationScoped
import java.util.Optional


import java.util.UUID


@ApplicationScoped
class ConsultantSlotConfigRepo : PanacheRepositoryBase<ConsultantSlotConfig, Long> {
    fun findListByConsultantId(consultantId: UUID): List<ConsultantSlotConfig> =
        // this will generate: SELECT c FROM ConsultantSlotConfig c WHERE c.consultantId = ?1
        find("consultantId", consultantId).list()

    fun findtByConsultantId(id: UUID): ConsultantSlotConfig? =
        find("consultantId", id).firstResult()

    fun findByIdOptional(id: Long): Optional<ConsultantSlotConfig> =
        Optional.ofNullable(findById(id))
}