package sirobilt.meghasanjivini.appointmentconfig

import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response

import org.acme.appointmentconfig.services.ConsultantSlotConfigService
import org.eclipse.microprofile.openapi.annotations.Operation
import org.eclipse.microprofile.openapi.annotations.tags.Tag
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter
import org.eclipse.microprofile.openapi.annotations.parameters.RequestBody
import org.eclipse.microprofile.openapi.annotations.media.Content
import org.eclipse.microprofile.openapi.annotations.media.Schema
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType
import sirobilt.meghasanjivini.appointmentconfig.dto.ConsultantSlotConfigResponseDto
import java.util.UUID


@Path("/consultants/{consultantId}/slot-configs")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Consultant Slot Config", description = "CRUD operations for consultant slot configurations")
class ConsultantSlotConfigController(
    private val configService: ConsultantSlotConfigService
) {

    // GET  /consultants/{consultantId}/slot-configs
    @GET
    @Operation(summary = "List all slot configurations for a consultant")
    @APIResponse(
        responseCode = "200",
        description = "Array of slot configurations",
        content = [Content(
            mediaType = MediaType.APPLICATION_JSON,
            schema = Schema(
                implementation = ConsultantSlotConfigResponseDto::class,
                type = SchemaType.ARRAY
            )
        )]
    )
    fun listConfigs(
        @QueryParam("consultantId")
        @Parameter(description = "Optional Consultant ID to filter by", required = false,
            schema = Schema(type = SchemaType.STRING, format = "uuid"))
        consultantId: UUID?
    ): List<ConsultantSlotConfigResponseDto> {
        return if (consultantId != null) {
            configService.getConfigsForConsultant(consultantId)
        } else {
            configService.getAllConfigs()
        }
    }




    // POST /consultants/{consultantId}/slot-configs
    @POST
    @Operation(summary = "Create a new slot configuration for a consultant")
    @APIResponse(
        responseCode = "201",
        description = "Created slot configuration",
        content = [Content(
            mediaType = MediaType.APPLICATION_JSON,
            schema = Schema(implementation = ConsultantSlotConfigDto::class)
        )]
    )
    fun createConfig(
        @PathParam("consultantId")
        @Parameter(description = "Consultant ID", required = true)
        consultantId: UUID,

        @RequestBody(
            description = "Slot configuration to create",
            required = true,
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = ConsultantSlotConfigDto::class)
            )]
        )
        dto: ConsultantSlotConfigDto
    ): Response {
        dto.consultantId = consultantId
        val created = configService.createConfig(dto)
        return Response.status(Response.Status.CREATED).entity(created).build()
    }


    // PUT  /consultants/{consultantId}/slot-configs/{configId}
    @PUT
    @Operation(summary = "Update an existing slot configuration")
    @APIResponse(
        responseCode = "200",
        description = "Updated slot configuration",
        content = [Content(
            mediaType = MediaType.APPLICATION_JSON,
            schema = Schema(implementation = ConsultantSlotConfigDto::class)
        )]
    )
    @APIResponse(responseCode = "404", description = "Configuration not found")
    fun updateConfig(
        @PathParam("consultantId")
        @Parameter(description = "Consultant ID", required = true)
        consultantId: UUID,

        @RequestBody(
            description = "Updated slot configuration",
            required = true,
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = ConsultantSlotConfigDto::class)
            )]
        )
        dto: ConsultantSlotConfigDto
    ): Response {
        dto.consultantId = consultantId
        val updated = configService.updateConfig(consultantId, dto)
        return Response.ok(updated).build()
    }
}

@Path("/slot-configs")
class SlotConfigController(private val configService: ConsultantSlotConfigService) {

    // DELETE Single record based on the configId (PK)
    @DELETE
    @Path("/{configId}")
    @Operation(summary = "Delete Single slot configuration based on configId (PK)")
    @APIResponse(responseCode = "204", description = "Slot configuration deleted successfully")
    @APIResponse(responseCode = "404", description = "Configuration not found")
    fun deleteConfigByConfigId(
        @PathParam("configId")
        @Parameter(description = "Config ID", required = true)
        id: Long
    ) {
        configService.deleteConfigByConfigId(id)
    }

    // DELETE Bulk records based on the consultantId
    @DELETE
    @Path("/consultant/{consultantId}")
    @Operation(summary = "May Delete bulk slot configurations based on the consultantId")
    @APIResponse(responseCode = "204", description = "Slot configuration(s) deleted successfully")
    @APIResponse(responseCode = "404", description = "Configuration not found")
    fun deleteConfigByConsultantId(
        @PathParam("consultantId")
        @Parameter(description = "Consultant ID", required = true)
        consultantId: UUID
    ) {
        configService.deleteConfigByConsultantId(consultantId)
    }
}
