package sirobilt.meghasanjivini.appointmentconfig

import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import org.acme.appointmentconfig.services.SlotService
import java.time.LocalDate
import org.eclipse.microprofile.openapi.annotations.Operation
import org.eclipse.microprofile.openapi.annotations.tags.Tag
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter
import org.eclipse.microprofile.openapi.annotations.parameters.RequestBody
import org.eclipse.microprofile.openapi.annotations.media.Content
import org.eclipse.microprofile.openapi.annotations.media.Schema
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType
import java.util.UUID

@Path("/consultants/{consultantId}/slots")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Appointment Slots", description = "Operations on appointment slots for consultants")
class SlotController(
    private val slotService: SlotService
) {

    data class SlotRangeRequest(
        @field:Schema(description = "Start date for slot generation", example = "2025-05-01")
        val from: LocalDate,
        @field:Schema(description = "End date for slot generation", example = "2025-05-07")
        val to: LocalDate
    )

    @POST
    @Path("/generate")
    @Operation(summary = "Generate appointment slots for a consultant")
    @APIResponse(responseCode = "200", description = "Number of slots generated")
    fun generateSlots(
        @PathParam("consultantId") @Parameter(description = "Consultant ID", required = true)
        consultantId: UUID,
        @RequestBody(
            description = "Date range for which to generate slots",
            required = true,
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = SlotRangeRequest::class)
            )]
        )
        range: SlotRangeRequest
    ): Response {
        val created = slotService.generateSlots(consultantId, range.from, range.to)
        return Response
            .ok(mapOf("created" to created))
            .build()
    }


    @GET
    @Operation(summary = "List open slots for a consultant, optionally filtered by date")
    @APIResponse(
        responseCode = "200",
        description = "Array of open appointment slots",
        content = [Content(
            mediaType = MediaType.APPLICATION_JSON,
            schema = Schema(implementation = AppointmentSlotDto::class, type = SchemaType.ARRAY)
        )]
    )
    fun listOpenSlots(
        @PathParam("consultantId")
        @Parameter(description = "Consultant ID", required = true)
        consultantId: UUID,

        @QueryParam("date")
        @Parameter(
            description = "Date for which to list slots (YYYY-MM-DD). Omit to get all dates",
            required = false
        )
        date: LocalDate?
    ): List<AppointmentSlotDto> =
        slotService.getAllSlotsForConsultant(consultantId, date)

}
