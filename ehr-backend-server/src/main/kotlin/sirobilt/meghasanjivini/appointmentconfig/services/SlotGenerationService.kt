package org.acme.appointmentconfig.services

import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional
import org.acme.appointmentconfig.repositories.AppointmentSlotRepo
import org.jboss.logging.Logger
import sirobilt.meghasanjivini.appointmentconfig.AppointmentSlot
import sirobilt.meghasanjivini.appointmentconfig.AppointmentSlotDto
import sirobilt.meghasanjivini.appointmentconfig.ConsultantSlotConfig
import sirobilt.meghasanjivini.appointmentconfig.ConsultantSlotConfigRepo
import sirobilt.meghasanjivini.common.enums.SlotAvailability
import java.time.LocalDate
import java.util.UUID

@ApplicationScoped
open class SlotService(
    private val configRepo: ConsultantSlotConfigRepo,
    private val slotRepo: AppointmentSlotRepo
) {
    companion object {
        private val log = Logger.getLogger(SlotService::class.java)
    }

    @Transactional
    fun generateSlots(consultantId: UUID, fromDate: LocalDate, toDate: LocalDate): Int {
        val configs = configRepo.findListByConsultantId(consultantId)
        log.infof("Found %d configs for consultant %s", configs.size, consultantId)

        var created = 0
        var date = fromDate
        while (!date.isAfter(toDate)) {
            log.debugf("▶ Checking generation for date: %s", date)
            configs.forEach { cfg ->
                log.debugf(
                    "⚙ Evaluating config: days=%s, effFrom=%s, effTo=%s, start=%s, end=%s, duration=%d",
                    cfg.daysOfWeek, cfg.effectiveFrom, cfg.effectiveTo, cfg.startTime, cfg.endTime, cfg.slotDuration
                )

                if (date.dayOfWeek in cfg.daysOfWeek
                    && !date.isBefore(cfg.effectiveFrom)
                    && (cfg.effectiveTo == null || !date.isAfter(cfg.effectiveTo))
                ) {
                    log.infof("✅ Config matched for %s (Day: %s)", date, date.dayOfWeek)
                    created += generateSlotsForDate(consultantId, cfg, date)
                } else {
                    log.debugf("❌ Config skipped for %s - did not match day or effective range", date)
                }
            }
            date = date.plusDays(1)
        }

        log.infof("✅ Total slots created: %d", created)
        return created
    }

    private fun generateSlotsForDate(
        consultantId: UUID,
        cfg: ConsultantSlotConfig,
        date: LocalDate
    ): Int {
        var slotStart = cfg.startTime
        var slotNum = 1
        var count = 0

        log.debugf("🗓 Generating slots for %s using config [%s–%s] (duration: %d min)",
            date, cfg.startTime, cfg.endTime, cfg.slotDuration)

        while (!slotStart.plusMinutes(cfg.slotDuration.toLong()).isAfter(cfg.endTime)) {
            val slotEnd = slotStart.plusMinutes(cfg.slotDuration.toLong())
            log.debugf("⏱ Attempting to generate slot #%d: [%s – %s]", slotNum, slotStart, slotEnd)

            val exists = slotRepo.find(
                "consultantId = ?1 and slotDate = ?2 and slotNumber = ?3",
                consultantId, date, slotNum
            ).firstResult() != null

            if (!exists) {
                try {
                    slotRepo.persistAndFlush(
                        AppointmentSlot().apply {
                            this.consultantId = consultantId
                            this.slotDate     = date
                            this.dayOfWeek    = date.dayOfWeek
                            this.startTime    = slotStart
                            this.endTime      = slotEnd
                            this.slotNumber   = slotNum
                            this.availability = SlotAvailability.OPEN
                        }
                    )
                    log.infof("🟢 Created slot #%d on %s [%s – %s]", slotNum, date, slotStart, slotEnd)
                    count++
                } catch (e: Exception) {
                    log.errorf("❌ Failed to create slot #%d on %s: %s", slotNum, date, e.message)
                }
            } else {
                log.debugf("⚠️ Slot #%d on %s already exists, skipping", slotNum, date)
            }

            slotStart = slotEnd
            slotNum++
        }

        return count
    }

    @Transactional
    fun getAllSlotsForConsultant(consultantId: UUID, date: LocalDate?): List<AppointmentSlotDto> {
        val query = if (date != null) {
            slotRepo.find(
                "consultantId = ?1 and slotDate = ?2 ORDER BY slotNumber",
                consultantId, date
            )
        } else {
            slotRepo.find(
                "consultantId = ?1 ORDER BY slotDate, slotNumber",
                consultantId
            )
        }

        val slots = query.list()
        log.infof("📋 Returning %d slots for consultant %s%s",
            slots.size, consultantId,
            if (date != null) " on $date" else ""
        )

        return slots.map { e ->
            AppointmentSlotDto().apply {
                this.consultantId = e.consultantId
                this.slotDate     = e.slotDate
                this.startTime    = e.startTime
                this.endTime      = e.endTime
                this.slotNumber   = e.slotNumber
                this.availability = e.availability
            }
        }
    }
}
