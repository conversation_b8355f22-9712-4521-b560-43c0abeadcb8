package sirobilt.meghasanjivini.waitingroom.dto

import java.time.LocalTime
import java.util.*

data class DoctorAvailabilityGroupResponse(
    val date: String,
    val departments: List<DoctorDepartmentGroup>
)

data class DoctorDepartmentGroup(
    val departmentId: String,
    val departmentName: String,
    val totalDoctors: Int,
    val availableDoctors: Int,
    val doctors: List<DoctorSlotDTO>
)

data class DoctorSlotDTO(
    val doctorId: UUID,
    val fullName: String,
    val isAvailable: Boolean,
    val nextAvailableTime: LocalTime?,
    val totalSlots: Int,
    val openSlots: Int,
    val experience: Int,
    val languages: List<String>
)
