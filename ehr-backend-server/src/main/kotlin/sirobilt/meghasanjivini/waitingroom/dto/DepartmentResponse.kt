package sirobilt.meghasanjivini.waitingroom.dto

data class DepartmentResponse(
    val success: Boolean,
    val data: Data
) {
    data class Data(
        val departments: List<DepartmentInfo>
    )
}

data class DepartmentInfo(
    val id: String,
    val name: String,
    val code: String,
    val description: String?,
    val color: String,
    val location: DepartmentLocation,
    val services: List<String>,
    val operatingHours: OperatingHours,
    val emergencyAvailable: Boolean,
    val contactInfo: ContactInfo?
)

data class DepartmentLocation(
    val floor: String,
    val wing: String? = null,
    val directions: String? = null
)

data class OperatingHours(
    val weekdays: TimeRange,
    val weekends: TimeRange? = null,
    val holidays: TimeRange? = null
)

data class TimeRange(
    val startTime: String,
    val endTime: String
)

data class ContactInfo(
    val phone: String?,
    val extension: String? = null
)
