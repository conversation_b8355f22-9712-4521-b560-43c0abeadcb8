package sirobilt.meghasanjivini.appointment.controller

import io.quarkus.test.junit.QuarkusTest
import io.restassured.RestAssured.given
import io.restassured.http.ContentType
import jakarta.inject.Inject
import org.hamcrest.CoreMatchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestMethodOrder
import org.junit.jupiter.api.MethodOrderer
import org.junit.jupiter.api.Order
import sirobilt.meghasanjivini.appointment.dto.AppointmentDTO
import sirobilt.meghasanjivini.appointment.repository.AppointmentRepository
import sirobilt.meghasanjivini.patientregistration.repository.PatientRepository
import sirobilt.meghasanjivini.patientregistration.model.*
import sirobilt.meghasanjivini.masterdata.repository.FacilityRepository
import sirobilt.meghasanjivini.masterdata.model.Facility
import java.time.LocalDateTime
import java.time.LocalDate

@QuarkusTest
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
class AppointmentControllerTest {

    @Inject
    lateinit var appointmentRepository: AppointmentRepository

    @Inject
    lateinit var patientRepository: PatientRepository

    @Inject
    lateinit var facilityRepository: FacilityRepository

    private val baseUrl = "/appointments"
    private lateinit var testPatientId: String
    private lateinit var testFacilityId: String

    @BeforeEach
    fun setup() {
        // Clean up test data
        appointmentRepository.deleteAll()
        
        // Create test patient
        val testPatient = Patient(
            upId = "TEST-PATIENT-001",
            facilityId = "1",
            firstName = "Test",
            lastName = "Patient",
            age = 30,
            gender = Gender.MALE
        )
        patientRepository.persist(testPatient)
        testPatientId = testPatient.upId

        // Create test facility if needed
        testFacilityId = "1"
    }

    @Test
    @Order(1)
    fun `should create appointment successfully`() {
        val appointmentDto = createValidAppointmentDto()

        given()
            .contentType(ContentType.JSON)
            .body(appointmentDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.appointmentId", notNullValue())
            .body("data.patientId", equalTo(testPatientId))
            .body("data.providerId", equalTo("PROVIDER-001"))
            .body("data.facilityId", equalTo(testFacilityId))
            .body("data.status", equalTo("Scheduled"))
    }

    @Test
    @Order(2)
    fun `should return 400 when required fields are missing`() {
        val invalidDto = AppointmentDTO(
            patientId = "", // Empty patient ID
            providerId = "", // Empty provider ID
            facilityId = "", // Empty facility ID
            appointmentDate = LocalDateTime.now(),
            startTime = LocalDateTime.now(),
            endTime = LocalDateTime.now().plusHours(1),
            duration = 60,
            type = "CONSULTATION",
            createdBy = "admin",
            updatedBy = "admin",
            slotNumber = 1
        )

        given()
            .contentType(ContentType.JSON)
            .body(invalidDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(400)
    }

    @Test
    @Order(3)
    fun `should retrieve appointment by ID successfully`() {
        // First create an appointment
        val appointmentDto = createValidAppointmentDto()
        val response = given()
            .contentType(ContentType.JSON)
            .body(appointmentDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(200)
            .extract()
            .response()

        val appointmentId = response.jsonPath().getString("data.appointmentId")

        // Then retrieve the appointment
        given()
            .`when`()
            .get("$baseUrl/$appointmentId")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.appointmentId", equalTo(appointmentId))
            .body("data.patientId", equalTo(testPatientId))
    }

    @Test
    @Order(4)
    fun `should return 404 when appointment not found`() {
        given()
            .`when`()
            .get("$baseUrl/non-existent-id")
            .then()
            .statusCode(404)
    }

    @Test
    @Order(5)
    fun `should update appointment successfully`() {
        // First create an appointment
        val appointmentDto = createValidAppointmentDto()
        val response = given()
            .contentType(ContentType.JSON)
            .body(appointmentDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(200)
            .extract()
            .response()

        val appointmentId = response.jsonPath().getString("data.appointmentId")

        // Update appointment
        val updateDto = appointmentDto.copy(
            status = "Confirmed",
            notes = "Updated appointment notes"
        )

        given()
            .contentType(ContentType.JSON)
            .body(updateDto)
            .`when`()
            .put("$baseUrl/$appointmentId")
            .then()
            .statusCode(200)
            .body("status", equalTo("Confirmed"))
            .body("notes", equalTo("Updated appointment notes"))
    }

    @Test
    @Order(6)
    fun `should cancel appointment successfully`() {
        // First create an appointment
        val appointmentDto = createValidAppointmentDto()
        val response = given()
            .contentType(ContentType.JSON)
            .body(appointmentDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(200)
            .extract()
            .response()

        val appointmentId = response.jsonPath().getString("data.appointmentId")

        // Cancel appointment
        given()
            .`when`()
            .delete("$baseUrl/$appointmentId")
            .then()
            .statusCode(204)
    }

    @Test
    @Order(7)
    fun `should query appointments with filters successfully`() {
        // Create multiple appointments
        val appointment1 = createValidAppointmentDto()
        val appointment2 = createValidAppointmentDto().copy(
            providerId = "PROVIDER-002",
            status = "Confirmed"
        )

        given().contentType(ContentType.JSON).body(appointment1).post(baseUrl)
        given().contentType(ContentType.JSON).body(appointment2).post(baseUrl)

        // Query by patient ID
        given()
            .queryParam("patientId", testPatientId)
            .`when`()
            .get("$baseUrl/query")
            .then()
            .statusCode(200)
            .body("data.size()", greaterThan(0))
    }

    @Test
    @Order(8)
    fun `should get appointment statistics successfully`() {
        // Create some test appointments
        val appointment1 = createValidAppointmentDto()
        val appointment2 = createValidAppointmentDto().copy(
            status = "Completed"
        )

        given().contentType(ContentType.JSON).body(appointment1).post(baseUrl)
        given().contentType(ContentType.JSON).body(appointment2).post(baseUrl)

        // Get statistics
        given()
            .queryParam("facilityId", testFacilityId)
            .`when`()
            .get("$baseUrl/stats")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.totalAppointments", greaterThanOrEqualTo(0))
            .body("data.scheduledAppointments", greaterThanOrEqualTo(0))
            .body("data.completedAppointments", greaterThanOrEqualTo(0))
    }

    @Test
    @Order(9)
    fun `should get today's appointments successfully`() {
        // Create an appointment for today
        val todayAppointment = createValidAppointmentDto().copy(
            appointmentDate = LocalDateTime.now(),
            startTime = LocalDateTime.now().plusHours(2),
            endTime = LocalDateTime.now().plusHours(3)
        )

        given().contentType(ContentType.JSON).body(todayAppointment).post(baseUrl)

        // Get today's appointments
        given()
            .queryParam("facilityId", testFacilityId)
            .`when`()
            .get("$baseUrl/today")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data", notNullValue())
    }

    @Test
    @Order(10)
    fun `should get upcoming appointments successfully`() {
        // Create an appointment for tomorrow
        val upcomingAppointment = createValidAppointmentDto().copy(
            appointmentDate = LocalDateTime.now().plusDays(1),
            startTime = LocalDateTime.now().plusDays(1).plusHours(2),
            endTime = LocalDateTime.now().plusDays(1).plusHours(3)
        )

        given().contentType(ContentType.JSON).body(upcomingAppointment).post(baseUrl)

        // Get upcoming appointments
        given()
            .queryParam("facilityId", testFacilityId)
            .queryParam("days", 7)
            .`when`()
            .get("$baseUrl/upcoming")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data", notNullValue())
    }

    @Test
    @Order(11)
    fun `should handle pagination for appointment queries`() {
        // Create multiple appointments
        repeat(5) { index ->
            val appointment = createValidAppointmentDto().copy(
                notes = "Appointment $index"
            )
            given().contentType(ContentType.JSON).body(appointment).post(baseUrl)
        }

        // Test pagination
        given()
            .queryParam("patientId", testPatientId)
            .queryParam("page", 0)
            .queryParam("size", 3)
            .`when`()
            .get("$baseUrl/query")
            .then()
            .statusCode(200)
            .body("data.size()", lessThanOrEqualTo(3))
    }

    @Test
    @Order(12)
    fun `should reschedule appointment successfully`() {
        // First create an appointment
        val appointmentDto = createValidAppointmentDto()
        val response = given()
            .contentType(ContentType.JSON)
            .body(appointmentDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(200)
            .extract()
            .response()

        val appointmentId = response.jsonPath().getString("data.appointmentId")

        // Reschedule appointment
        val rescheduleDto = mapOf(
            "newStartTime" to LocalDateTime.now().plusDays(2).toString(),
            "newEndTime" to LocalDateTime.now().plusDays(2).plusHours(1).toString(),
            "reason" to "Patient request"
        )

        given()
            .contentType(ContentType.JSON)
            .body(rescheduleDto)
            .`when`()
            .patch("$baseUrl/$appointmentId/reschedule")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
    }

    private fun createValidAppointmentDto(): AppointmentDTO {
        val now = LocalDateTime.now().plusDays(1) // Schedule for tomorrow
        return AppointmentDTO(
            patientId = testPatientId,
            providerId = "PROVIDER-001",
            facilityId = testFacilityId,
            appointmentDate = now,
            startTime = now.withHour(10).withMinute(0),
            endTime = now.withHour(11).withMinute(0),
            duration = 60,
            type = "CONSULTATION",
            status = "Scheduled",
            priority = "Normal",
            title = "Regular Checkup",
            description = "Routine medical consultation",
            reason = "Regular health checkup",
            createdBy = "admin",
            updatedBy = "admin",
            slotNumber = 1
        )
    }
}
