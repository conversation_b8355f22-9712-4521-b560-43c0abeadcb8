package sirobilt.meghasanjivini.patientregistration.controller

import io.quarkus.test.junit.QuarkusTest
import io.restassured.RestAssured.given
import io.restassured.http.ContentType
import jakarta.inject.Inject
import org.hamcrest.CoreMatchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestMethodOrder
import org.junit.jupiter.api.MethodOrderer
import org.junit.jupiter.api.Order
import sirobilt.meghasanjivini.patientregistration.dto.PatientRegistrationDto
import sirobilt.meghasanjivini.patientregistration.model.*
import sirobilt.meghasanjivini.patientregistration.repository.PatientRepository
import java.time.LocalDate

@QuarkusTest
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
class PatientControllerTest {

    @Inject
    lateinit var patientRepository: PatientRepository

    private val baseUrl = "/patients"

    @BeforeEach
    fun setup() {
        // Clean up test data before each test
        patientRepository.deleteAll()
    }

    @Test
    @Order(1)
    fun `should register patient successfully with valid data`() {
        val patientDto = createValidPatientRegistrationDto()

        given()
            .contentType(ContentType.JSON)
            .body(patientDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(201)
            .body("patientId", notNullValue())
            .body("facilityId", equalTo("2"))
            .body("firstName", equalTo("John"))
            .body("lastName", equalTo("Doe"))
            .body("fullName", equalTo("John Doe"))
            .body("age", equalTo(30))
            .body("gender", equalTo("MALE"))
    }

    @Test
    @Order(2)
    fun `should return 400 when required fields are missing`() {
        val invalidDto = PatientRegistrationDto(
            facilityId = "", // Empty facility ID
            firstName = null, // Missing first name
            lastName = null,  // Missing last name
            age = null,       // Missing age
            gender = null     // Missing gender
        )

        given()
            .contentType(ContentType.JSON)
            .body(invalidDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(400)
    }

    @Test
    @Order(3)
    fun `should return 400 when age is out of valid range`() {
        val invalidAgeDto = createValidPatientRegistrationDto().copy(age = 200)

        given()
            .contentType(ContentType.JSON)
            .body(invalidAgeDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(400)
    }

    @Test
    @Order(4)
    fun `should retrieve patient by ID successfully`() {
        // First register a patient
        val patientDto = createValidPatientRegistrationDto()
        val response = given()
            .contentType(ContentType.JSON)
            .body(patientDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(201)
            .extract()
            .response()

        val patientId = response.jsonPath().getString("patientId")

        // Then retrieve the patient
        given()
            .`when`()
            .get("$baseUrl/$patientId")
            .then()
            .statusCode(200)
            .body("patientId", equalTo(patientId))
            .body("firstName", equalTo("John"))
            .body("lastName", equalTo("Doe"))
    }

    @Test
    @Order(5)
    fun `should return 404 when patient not found`() {
        given()
            .`when`()
            .get("$baseUrl/non-existent-id")
            .then()
            .statusCode(404)
    }

    @Test
    @Order(6)
    fun `should update patient successfully`() {
        // First register a patient
        val patientDto = createValidPatientRegistrationDto()
        val response = given()
            .contentType(ContentType.JSON)
            .body(patientDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(201)
            .extract()
            .response()

        val patientId = response.jsonPath().getString("patientId")

        // Update patient data
        val updateDto = mapOf(
            "firstName" to "Jane",
            "lastName" to "Smith",
            "age" to 25
        )

        given()
            .contentType(ContentType.JSON)
            .body(updateDto)
            .`when`()
            .put("$baseUrl/$patientId")
            .then()
            .statusCode(200)
            .body("firstName", equalTo("Jane"))
            .body("lastName", equalTo("Smith"))
            .body("age", equalTo(25))
    }

    @Test
    @Order(7)
    fun `should search patients by query successfully`() {
        // Register multiple patients
        val patient1 = createValidPatientRegistrationDto()
        val patient2 = createValidPatientRegistrationDto().copy(
            firstName = "Jane",
            lastName = "Smith"
        )

        given().contentType(ContentType.JSON).body(patient1).post(baseUrl)
        given().contentType(ContentType.JSON).body(patient2).post(baseUrl)

        // Search by first name
        given()
            .queryParam("query", "John")
            .`when`()
            .get("$baseUrl/query")
            .then()
            .statusCode(200)
            .body("patients.size()", greaterThan(0))
            .body("totalCount", greaterThan(0))
    }

    @Test
    @Order(8)
    fun `should soft delete patient successfully`() {
        // First register a patient
        val patientDto = createValidPatientRegistrationDto()
        val response = given()
            .contentType(ContentType.JSON)
            .body(patientDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(201)
            .extract()
            .response()

        val patientId = response.jsonPath().getString("patientId")

        // Soft delete the patient
        given()
            .`when`()
            .delete("$baseUrl/$patientId")
            .then()
            .statusCode(204)

        // Verify patient is soft deleted (should return 404 or marked as deleted)
        given()
            .`when`()
            .get("$baseUrl/$patientId")
            .then()
            .statusCode(404)
    }

    @Test
    @Order(9)
    fun `should handle duplicate detection during registration`() {
        val patientDto = createValidPatientRegistrationDto()

        // Register first patient
        given()
            .contentType(ContentType.JSON)
            .body(patientDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(201)

        // Try to register same patient again (should trigger duplicate detection)
        given()
            .contentType(ContentType.JSON)
            .body(patientDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(anyOf(equalTo(201), equalTo(409))) // Either created or conflict
    }

    @Test
    @Order(10)
    fun `should return paginated results for patient list`() {
        // Register multiple patients
        repeat(5) { index ->
            val patientDto = createValidPatientRegistrationDto().copy(
                firstName = "Patient$index"
            )
            given().contentType(ContentType.JSON).body(patientDto).post(baseUrl)
        }

        // Test pagination
        given()
            .queryParam("page", 0)
            .queryParam("size", 3)
            .`when`()
            .get("$baseUrl/query")
            .then()
            .statusCode(200)
            .body("patients.size()", lessThanOrEqualTo(3))
            .body("totalCount", greaterThanOrEqualTo(5))
    }

    private fun createValidPatientRegistrationDto() = PatientRegistrationDto(
        facilityId = "2",
        identifierType = IdentifierType.ABHA,
        identifierNumber = "12-3456-7890-${System.currentTimeMillis()}",
        title = Title.MR,
        firstName = "John",
        lastName = "Doe",
        dateOfBirth = LocalDate.of(1993, 1, 1),
        age = 30,
        gender = Gender.MALE,
        bloodGroup = BloodGroup.O_POSITIVE,
        maritalStatus = MaritalStatus.SINGLE,
        citizenship = "Indian",
        religion = "Hindu",
        occupation = "Engineer"
    )
}
