package sirobilt.meghasanjivini.patientregistration.service

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.junit.mockito.InjectMock
import jakarta.inject.Inject
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.assertThrows
import org.mockito.Mockito.*
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import sirobilt.meghasanjivini.patientregistration.dto.*
import sirobilt.meghasanjivini.patientregistration.model.*
import sirobilt.meghasanjivini.patientregistration.repository.*
import sirobilt.meghasanjivini.appointment.service.TokenService
import jakarta.ws.rs.NotFoundException
import java.time.LocalDate
import java.time.OffsetDateTime

@QuarkusTest
class PatientServiceTest {

    @Inject
    lateinit var patientService: PatientService

    @InjectMock
    lateinit var patientRepository: PatientRepository

    @InjectMock
    lateinit var contactRepository: PatientContactRepository

    @InjectMock
    lateinit var addressRepository: PatientAddressRepository

    @InjectMock
    lateinit var emergencyRepository: EmergencyContactRepository

    @InjectMock
    lateinit var insuranceRepository: PatientInsuranceRepository

    @InjectMock
    lateinit var billingReferralRepository: BillingReferralRepository

    @InjectMock
    lateinit var infoSharingRepository: InformationSharingRepository

    @InjectMock
    lateinit var referralRepository: ReferralRepository

    @InjectMock
    lateinit var tokenService: TokenService

    @InjectMock
    lateinit var duplicateDetectionService: DuplicateDetectionService

    private lateinit var samplePatient: Patient
    private lateinit var samplePatientDto: PatientRegistrationDto

    @BeforeEach
    fun setup() {
        samplePatient = createSamplePatient()
        samplePatientDto = createSamplePatientRegistrationDto()
    }

    @Test
    fun `should register patient successfully`() {
        // Given
        val expectedMrn = "002-00-0000-0001"
        val duplicateResult = DuplicateDetectionResult(
            isDuplicate = false,
            confidenceLevel = ConfidenceLevel.LOW,
            overallScore = 0,
            potentialDuplicates = emptyList(),
            action = DuplicateAction.ALLOW_REGISTRATION,
            message = "No duplicates found"
        )

        whenever(duplicateDetectionService.checkForDuplicates(any())).thenReturn(duplicateResult)
        whenever(patientRepository.findLastMrnForFacility(any())).thenReturn(null)
        whenever(patientRepository.persist(any<Patient>())).thenAnswer { it.arguments[0] }
        whenever(contactRepository.persist(any<List<PatientContact>>())).thenReturn(Unit)

        // When
        val result = patientService.register(samplePatientDto)

        // Then
        assertNotNull(result)
        assertEquals("002-00-0000-0001", result.patientId)
        assertEquals("John", result.firstName)
        assertEquals("Doe", result.lastName)
        assertEquals("John Doe", result.fullName)
        assertEquals(30, result.age)
        assertEquals(Gender.MALE, result.gender)

        verify(patientRepository).persist(any<Patient>())
        verify(duplicateDetectionService).checkForDuplicates(any())
    }

    @Test
    fun `should handle duplicate detection during registration`() {
        // Given
        val duplicateResult = DuplicateDetectionResult(
            isDuplicate = true,
            confidenceLevel = ConfidenceLevel.HIGH,
            overallScore = 90,
            potentialDuplicates = listOf(
                PotentialDuplicate(
                    patientId = "002-00-0000-0001",
                    score = 90,
                    matchingFields = mapOf(
                        "firstName" to FieldMatch(MatchType.EXACT, 30),
                        "lastName" to FieldMatch(MatchType.EXACT, 30),
                        "dateOfBirth" to FieldMatch(MatchType.EXACT, 30)
                    )
                )
            ),
            action = DuplicateAction.FLAG_FOR_REVIEW,
            message = "High confidence duplicate detected"
        )

        whenever(duplicateDetectionService.checkForDuplicates(any())).thenReturn(duplicateResult)

        // When & Then
        val exception = assertThrows<DuplicatePatientException> {
            patientService.register(samplePatientDto)
        }

        assertTrue(exception.message!!.contains("duplicate"))
        verify(patientRepository, never()).persist(any<Patient>())
    }

    @Test
    fun `should find patient by ID successfully`() {
        // Given
        val patientId = "002-00-0000-0001"
        whenever(patientRepository.findById(patientId)).thenReturn(samplePatient)

        // When
        val result = patientService.findById(patientId)

        // Then
        assertNotNull(result)
        assertEquals(patientId, result.patientId)
        assertEquals("John", result.firstName)
        assertEquals("Doe", result.lastName)
    }

    @Test
    fun `should throw NotFoundException when patient not found`() {
        // Given
        val patientId = "non-existent-id"
        whenever(patientRepository.findById(patientId)).thenReturn(null)

        // When & Then
        assertThrows<NotFoundException> {
            patientService.findById(patientId)
        }
    }

    @Test
    fun `should update patient successfully`() {
        // Given
        val patientId = "002-00-0000-0001"
        val updateDto = UpdatePatientDto(
            firstName = "Jane",
            lastName = "Smith",
            age = 25,
            gender = Gender.FEMALE
        )

        whenever(patientRepository.findById(patientId)).thenReturn(samplePatient)
        whenever(patientRepository.persist(any<Patient>())).thenAnswer { it.arguments[0] }

        // When
        val result = patientService.update(patientId, updateDto)

        // Then
        assertNotNull(result)
        assertEquals("Jane", result.firstName)
        assertEquals("Smith", result.lastName)
        assertEquals(25, result.age)
        assertEquals(Gender.FEMALE, result.gender)

        verify(patientRepository).persist(any<Patient>())
    }

    @Test
    fun `should soft delete patient successfully`() {
        // Given
        val patientId = "002-00-0000-0001"
        whenever(patientRepository.softDeleteById(patientId)).thenReturn(true)

        // When
        val result = patientService.softDelete(patientId)

        // Then
        assertTrue(result)
        verify(patientRepository).softDeleteById(patientId)
    }

    @Test
    fun `should return false when soft delete fails`() {
        // Given
        val patientId = "non-existent-id"
        whenever(patientRepository.softDeleteById(patientId)).thenReturn(false)

        // When
        val result = patientService.softDelete(patientId)

        // Then
        assertFalse(result)
    }

    @Test
    fun `should search patients by query successfully`() {
        // Given
        val query = "John"
        val patients = listOf(samplePatient)
        whenever(patientRepository.searchByQuery(query, 0, 10)).thenReturn(patients)

        // When
        val result = patientService.searchByQuery(query, 0, 10)

        // Then
        assertNotNull(result)
        assertEquals(1, result.size)
        assertEquals("John", result[0].firstName)
    }

    @Test
    fun `should generate correct MRN for facility`() {
        // Given
        val facilityId = "2"
        whenever(patientRepository.findLastMrnForFacility(facilityId)).thenReturn(null)

        // When
        val mrn = patientService.generateNextMrn(facilityId, null)

        // Then
        assertEquals("002-00-0000-0001", mrn)
    }

    @Test
    fun `should increment MRN correctly when existing patients exist`() {
        // Given
        val facilityId = "2"
        val lastMrn = "002-00-0000-0005"
        whenever(patientRepository.findLastMrnForFacility(facilityId)).thenReturn(lastMrn)

        // When
        val mrn = patientService.generateNextMrn(facilityId, null)

        // Then
        assertEquals("002-00-0000-0006", mrn)
    }

    @Test
    fun `should list all patients with pagination`() {
        // Given
        val patients = listOf(samplePatient)
        val totalCount = 1L
        whenever(patientRepository.findAll()).thenReturn(mock())
        whenever(patientRepository.count()).thenReturn(totalCount)

        // Mock the page behavior
        val mockPage = mock<io.quarkus.panache.common.Page>()
        whenever(mockPage.list<Patient>()).thenReturn(patients)

        // When
        val result = patientService.listAllWithCount(0, 10)

        // Then
        assertNotNull(result)
        assertEquals(1, result.patients.size)
        assertEquals(totalCount, result.totalCount)
    }

    private fun createSamplePatient(): Patient {
        return Patient(
            upId = "002-00-0000-0001",
            facilityId = "2",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "12-**************",
            title = Title.MR,
            firstName = "John",
            lastName = "Doe",
            dateOfBirth = LocalDate.of(1993, 1, 1),
            age = 30,
            gender = Gender.MALE,
            bloodGroup = BloodGroup.O_POSITIVE,
            maritalStatus = MaritalStatus.SINGLE,
            citizenship = "Indian",
            religion = "Hindu",
            occupation = "Engineer"
        ).apply {
            registrationDate = OffsetDateTime.now()
            isActive = true
            isDeceased = false
        }
    }

    private fun createSamplePatientRegistrationDto(): PatientRegistrationDto {
        return PatientRegistrationDto(
            facilityId = "2",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "12-**************",
            title = Title.MR,
            firstName = "John",
            lastName = "Doe",
            dateOfBirth = LocalDate.of(1993, 1, 1),
            age = 30,
            gender = Gender.MALE,
            bloodGroup = BloodGroup.O_POSITIVE,
            maritalStatus = MaritalStatus.SINGLE,
            citizenship = "Indian",
            religion = "Hindu",
            occupation = "Engineer"
        )
    }
}
