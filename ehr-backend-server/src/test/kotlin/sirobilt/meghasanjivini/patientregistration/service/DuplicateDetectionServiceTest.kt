package sirobilt.meghasanjivini.patientregistration.service

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.junit.mockito.InjectMock
import jakarta.inject.Inject
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import org.mockito.Mockito.*
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import sirobilt.meghasanjivini.patientregistration.dto.*
import sirobilt.meghasanjivini.patientregistration.model.*
import sirobilt.meghasanjivini.patientregistration.repository.PatientRepository
import java.time.LocalDate

@QuarkusTest
class DuplicateDetectionServiceTest {

    @Inject
    lateinit var duplicateDetectionService: DuplicateDetectionService

    @InjectMock
    lateinit var patientRepository: PatientRepository

    @InjectMock
    lateinit var configService: DuplicateDetectionConfigService

    @InjectMock
    lateinit var matchingEngine: PatientMatchingEngine

    @InjectMock
    lateinit var auditService: DuplicateDetectionAuditService

    private lateinit var sampleConfig: DuplicateDetectionConfig
    private lateinit var candidatePatient: PatientRegistrationDto
    private lateinit var existingPatient: Patient

    @BeforeEach
    fun setup() {
        sampleConfig = createSampleConfig()
        candidatePatient = createCandidatePatient()
        existingPatient = createExistingPatient()

        whenever(configService.getCurrentConfig()).thenReturn(sampleConfig)
    }

    @Test
    fun `should allow registration when no duplicates found`() {
        // Given
        whenever(patientRepository.findPotentialDuplicates(any(), any(), any(), any(), any()))
            .thenReturn(emptyList())

        // When
        val result = duplicateDetectionService.checkForDuplicates(candidatePatient)

        // Then
        assertEquals(DuplicateAction.ALLOW_REGISTRATION, result.action)
        assertEquals(0, result.confidence)
        assertTrue(result.matches.isEmpty())
        assertEquals("No potential duplicates found", result.message)
    }

    @Test
    fun `should detect high confidence duplicate with exact ABHA match`() {
        // Given
        val existingPatients = listOf(existingPatient)
        whenever(patientRepository.findPotentialDuplicates(any(), any(), any(), any(), any()))
            .thenReturn(existingPatients)

        val matchResult = DuplicateMatchResult(
            patientId = existingPatient.upId,
            totalScore = 95,
            matchingFields = mapOf(
                "identifierNumber" to 40,
                "firstName" to 30,
                "lastName" to 25
            )
        )
        whenever(matchingEngine.calculateMatchScore(any(), any())).thenReturn(matchResult)

        // When
        val result = duplicateDetectionService.checkForDuplicates(candidatePatient)

        // Then
        assertEquals(DuplicateAction.BLOCK_REGISTRATION, result.action)
        assertEquals(95, result.confidence)
        assertEquals(1, result.matches.size)
        assertEquals("002-00-0000-0001", result.matches[0].patientId)
        assertTrue(result.message.contains("High confidence duplicate"))
    }

    @Test
    fun `should require manual review for medium confidence duplicate`() {
        // Given
        val existingPatients = listOf(existingPatient)
        whenever(patientRepository.findPotentialDuplicates(any(), any(), any(), any(), any()))
            .thenReturn(existingPatients)

        val matchResult = DuplicateMatchResult(
            patientId = existingPatient.upId,
            totalScore = 75,
            matchingFields = mapOf(
                "firstName" to 30,
                "lastName" to 25,
                "dateOfBirth" to 20
            )
        )
        whenever(matchingEngine.calculateMatchScore(any(), any())).thenReturn(matchResult)

        // When
        val result = duplicateDetectionService.checkForDuplicates(candidatePatient)

        // Then
        assertEquals(DuplicateAction.REQUIRE_MANUAL_REVIEW, result.action)
        assertEquals(75, result.confidence)
        assertEquals(1, result.matches.size)
        assertTrue(result.message.contains("Medium confidence duplicate"))
    }

    @Test
    fun `should allow registration for low confidence match`() {
        // Given
        val existingPatients = listOf(existingPatient)
        whenever(patientRepository.findPotentialDuplicates(any(), any(), any(), any(), any()))
            .thenReturn(existingPatients)

        val matchResult = DuplicateMatchResult(
            patientId = existingPatient.upId,
            totalScore = 50,
            matchingFields = mapOf(
                "firstName" to 30,
                "lastName" to 20
            )
        )
        whenever(matchingEngine.calculateMatchScore(any(), any())).thenReturn(matchResult)

        // When
        val result = duplicateDetectionService.checkForDuplicates(candidatePatient)

        // Then
        assertEquals(DuplicateAction.ALLOW_REGISTRATION, result.action)
        assertEquals(50, result.confidence)
        assertTrue(result.matches.isEmpty()) // Low confidence matches are not included
    }

    @Test
    fun `should handle multiple potential duplicates and return highest score`() {
        // Given
        val existingPatient2 = createExistingPatient().copy(upId = "002-00-0000-0002")
        val existingPatients = listOf(existingPatient, existingPatient2)
        
        whenever(patientRepository.findPotentialDuplicates(any(), any(), any(), any(), any()))
            .thenReturn(existingPatients)

        val matchResult1 = DuplicateMatchResult(
            patientId = existingPatient.upId,
            totalScore = 75,
            matchingFields = mapOf("firstName" to 30, "lastName" to 25, "dateOfBirth" to 20)
        )
        val matchResult2 = DuplicateMatchResult(
            patientId = existingPatient2.upId,
            totalScore = 90,
            matchingFields = mapOf("firstName" to 30, "lastName" to 25, "identifierNumber" to 35)
        )

        whenever(matchingEngine.calculateMatchScore(candidatePatient, existingPatient))
            .thenReturn(matchResult1)
        whenever(matchingEngine.calculateMatchScore(candidatePatient, existingPatient2))
            .thenReturn(matchResult2)

        // When
        val result = duplicateDetectionService.checkForDuplicates(candidatePatient)

        // Then
        assertEquals(DuplicateAction.BLOCK_REGISTRATION, result.action)
        assertEquals(90, result.confidence)
        assertEquals(2, result.matches.size)
        
        // Verify matches are sorted by confidence (highest first)
        assertEquals("002-00-0000-0002", result.matches[0].patientId)
        assertEquals(90, result.matches[0].confidence)
        assertEquals("002-00-0000-0001", result.matches[1].patientId)
        assertEquals(75, result.matches[1].confidence)
    }

    @Test
    fun `should handle timeout gracefully`() {
        // Given
        whenever(patientRepository.findPotentialDuplicates(any(), any(), any(), any(), any()))
            .thenThrow(RuntimeException("Database timeout"))

        // When
        val result = duplicateDetectionService.checkForDuplicates(candidatePatient)

        // Then
        assertEquals(DuplicateAction.ALLOW_REGISTRATION, result.action)
        assertEquals(0, result.confidence)
        assertTrue(result.matches.isEmpty())
        assertTrue(result.message.contains("timeout") || result.message.contains("error"))
    }

    @Test
    fun `should respect disabled configuration`() {
        // Given
        val disabledConfig = sampleConfig.copy(enabled = false)
        whenever(configService.getCurrentConfig()).thenReturn(disabledConfig)

        // When
        val result = duplicateDetectionService.checkForDuplicates(candidatePatient)

        // Then
        assertEquals(DuplicateAction.ALLOW_REGISTRATION, result.action)
        assertEquals(0, result.confidence)
        assertTrue(result.matches.isEmpty())
        assertEquals("Duplicate detection is disabled", result.message)
        
        // Verify no database queries were made
        verify(patientRepository, never()).findPotentialDuplicates(any(), any(), any(), any(), any())
    }

    @Test
    fun `should audit duplicate detection results when enabled`() {
        // Given
        val auditConfig = sampleConfig.copy(auditEnabled = true)
        whenever(configService.getCurrentConfig()).thenReturn(auditConfig)
        whenever(patientRepository.findPotentialDuplicates(any(), any(), any(), any(), any()))
            .thenReturn(emptyList())

        // When
        duplicateDetectionService.checkForDuplicates(candidatePatient)

        // Then
        verify(auditService).logDetectionEvent(any(), any(), any())
    }

    @Test
    fun `should not audit when audit is disabled`() {
        // Given
        val noAuditConfig = sampleConfig.copy(auditEnabled = false)
        whenever(configService.getCurrentConfig()).thenReturn(noAuditConfig)
        whenever(patientRepository.findPotentialDuplicates(any(), any(), any(), any(), any()))
            .thenReturn(emptyList())

        // When
        duplicateDetectionService.checkForDuplicates(candidatePatient)

        // Then
        verify(auditService, never()).logDetectionEvent(any(), any(), any())
    }

    private fun createSampleConfig(): DuplicateDetectionConfig {
        return DuplicateDetectionConfig(
            enabled = true,
            highThreshold = 85,
            mediumThreshold = 70,
            weights = DuplicateDetectionWeights(
                nameExact = 40,
                nameFuzzy = 30,
                dateOfBirth = 35,
                phone = 25,
                email = 20,
                address = 15,
                identifier = 40
            ),
            fuzzyThreshold = 80,
            timeoutSeconds = 2,
            auditEnabled = true
        )
    }

    private fun createCandidatePatient(): PatientRegistrationDto {
        return PatientRegistrationDto(
            facilityId = "2",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "12-3456-7890-1234",
            firstName = "John",
            lastName = "Doe",
            dateOfBirth = LocalDate.of(1993, 1, 1),
            age = 30,
            gender = Gender.MALE
        )
    }

    private fun createExistingPatient(): Patient {
        return Patient(
            upId = "002-00-0000-0001",
            facilityId = "2",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "12-3456-7890-1234",
            firstName = "John",
            lastName = "Doe",
            dateOfBirth = LocalDate.of(1993, 1, 1),
            age = 30,
            gender = Gender.MALE
        )
    }
}
