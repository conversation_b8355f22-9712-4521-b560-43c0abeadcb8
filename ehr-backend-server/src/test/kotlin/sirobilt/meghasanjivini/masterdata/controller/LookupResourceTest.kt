package sirobilt.meghasanjivini.masterdata.controller

import io.quarkus.test.junit.QuarkusTest
import io.restassured.RestAssured.given
import io.restassured.http.ContentType
import jakarta.inject.Inject
import org.hamcrest.CoreMatchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestMethodOrder
import org.junit.jupiter.api.MethodOrderer
import org.junit.jupiter.api.Order
import sirobilt.meghasanjivini.masterdata.model.LookupValue
import sirobilt.meghasanjivini.masterdata.repository.LookupRepository
import sirobilt.meghasanjivini.masterdata.dto.LookupValueCreateDTO
import sirobilt.meghasanjivini.masterdata.dto.LookupValueUpdateDTO
import java.util.UUID

@QuarkusTest
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
class LookupResourceTest {

    @Inject
    lateinit var lookupRepository: LookupRepository

    private val baseUrl = "/lookup-values"

    @BeforeEach
    fun setup() {
        // Clean up test data
        lookupRepository.deleteAll()
        
        // Create some test lookup values
        createTestLookupValues()
    }

    @Test
    @Order(1)
    fun `should get lookup values by category successfully`() {
        given()
            .`when`()
            .get("$baseUrl/GENDER")
            .then()
            .statusCode(200)
            .body("size()", greaterThan(0))
            .body("[0].category", equalTo("GENDER"))
            .body("[0].code", notNullValue())
            .body("[0].displayName", notNullValue())
    }

    @Test
    @Order(2)
    fun `should return empty list for non-existent category`() {
        given()
            .`when`()
            .get("$baseUrl/NON_EXISTENT_CATEGORY")
            .then()
            .statusCode(200)
            .body("size()", equalTo(0))
    }

    @Test
    @Order(3)
    fun `should get all lookup values successfully`() {
        given()
            .`when`()
            .get(baseUrl)
            .then()
            .statusCode(200)
            .body("size()", greaterThan(0))
    }

    @Test
    @Order(4)
    fun `should create new lookup value successfully`() {
        val createDto = LookupValueCreateDTO(
            category = "BLOOD_GROUP",
            code = "AB_NEGATIVE",
            displayName = "AB-",
            sortOrder = 8,
            active = true
        )

        given()
            .contentType(ContentType.JSON)
            .body(createDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(201)
            .body("category", equalTo("BLOOD_GROUP"))
            .body("code", equalTo("AB_NEGATIVE"))
            .body("displayName", equalTo("AB-"))
            .body("sortOrder", equalTo(8))
            .body("active", equalTo(true))
    }

    @Test
    @Order(5)
    fun `should return 400 when creating lookup value with invalid data`() {
        val invalidDto = LookupValueCreateDTO(
            category = "", // Empty category
            code = "", // Empty code
            displayName = "", // Empty display name
            sortOrder = -1, // Invalid sort order
            active = true
        )

        given()
            .contentType(ContentType.JSON)
            .body(invalidDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(400)
    }

    @Test
    @Order(6)
    fun `should update lookup value successfully`() {
        // First create a lookup value
        val createDto = LookupValueCreateDTO(
            category = "MARITAL_STATUS",
            code = "SINGLE",
            displayName = "Single",
            sortOrder = 1,
            active = true
        )

        val response = given()
            .contentType(ContentType.JSON)
            .body(createDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(201)
            .extract()
            .response()

        val lookupId = UUID.fromString(response.jsonPath().getString("id"))

        // Update the lookup value
        val updateDto = LookupValueUpdateDTO(
            displayName = "Unmarried",
            sortOrder = 2,
            active = false
        )

        given()
            .contentType(ContentType.JSON)
            .body(updateDto)
            .`when`()
            .put("$baseUrl/$lookupId")
            .then()
            .statusCode(200)
            .body("displayName", equalTo("Unmarried"))
            .body("sortOrder", equalTo(2))
            .body("active", equalTo(false))
    }

    @Test
    @Order(7)
    fun `should return 404 when updating non-existent lookup value`() {
        val nonExistentId = UUID.randomUUID()
        val updateDto = LookupValueUpdateDTO(
            displayName = "Updated",
            sortOrder = 1,
            active = true
        )

        given()
            .contentType(ContentType.JSON)
            .body(updateDto)
            .`when`()
            .put("$baseUrl/$nonExistentId")
            .then()
            .statusCode(404)
    }

    @Test
    @Order(8)
    fun `should delete lookup value successfully`() {
        // First create a lookup value
        val createDto = LookupValueCreateDTO(
            category = "TITLE",
            code = "DR",
            displayName = "Doctor",
            sortOrder = 1,
            active = true
        )

        val response = given()
            .contentType(ContentType.JSON)
            .body(createDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(201)
            .extract()
            .response()

        val lookupId = UUID.fromString(response.jsonPath().getString("id"))

        // Delete the lookup value
        given()
            .`when`()
            .delete("$baseUrl/$lookupId")
            .then()
            .statusCode(204)

        // Verify it's deleted
        given()
            .`when`()
            .get("$baseUrl/TITLE")
            .then()
            .statusCode(200)
            .body("find { it.code == 'DR' }", nullValue())
    }

    @Test
    @Order(9)
    fun `should return 404 when deleting non-existent lookup value`() {
        val nonExistentId = UUID.randomUUID()

        given()
            .`when`()
            .delete("$baseUrl/$nonExistentId")
            .then()
            .statusCode(404)
    }

    @Test
    @Order(10)
    fun `should return lookup values sorted by sortOrder`() {
        // Create multiple lookup values with different sort orders
        val lookupValues = listOf(
            LookupValueCreateDTO("PRIORITY", "HIGH", "High", 3, true),
            LookupValueCreateDTO("PRIORITY", "LOW", "Low", 1, true),
            LookupValueCreateDTO("PRIORITY", "MEDIUM", "Medium", 2, true)
        )

        lookupValues.forEach { dto ->
            given()
                .contentType(ContentType.JSON)
                .body(dto)
                .post(baseUrl)
        }

        // Verify they are returned in sort order
        given()
            .`when`()
            .get("$baseUrl/PRIORITY")
            .then()
            .statusCode(200)
            .body("size()", equalTo(3))
            .body("[0].code", equalTo("LOW"))
            .body("[0].sortOrder", equalTo(1))
            .body("[1].code", equalTo("MEDIUM"))
            .body("[1].sortOrder", equalTo(2))
            .body("[2].code", equalTo("HIGH"))
            .body("[2].sortOrder", equalTo(3))
    }

    @Test
    @Order(11)
    fun `should filter active lookup values only`() {
        // Create active and inactive lookup values
        val activeLookup = LookupValueCreateDTO("STATUS", "ACTIVE", "Active", 1, true)
        val inactiveLookup = LookupValueCreateDTO("STATUS", "INACTIVE", "Inactive", 2, false)

        given().contentType(ContentType.JSON).body(activeLookup).post(baseUrl)
        given().contentType(ContentType.JSON).body(inactiveLookup).post(baseUrl)

        // When filtering by active status (assuming the API supports this)
        given()
            .queryParam("active", true)
            .`when`()
            .get("$baseUrl/STATUS")
            .then()
            .statusCode(200)
            .body("size()", equalTo(1))
            .body("[0].code", equalTo("ACTIVE"))
            .body("[0].active", equalTo(true))
    }

    @Test
    @Order(12)
    fun `should handle duplicate code creation gracefully`() {
        val originalDto = LookupValueCreateDTO(
            category = "DUPLICATE_TEST",
            code = "DUPLICATE_CODE",
            displayName = "Original",
            sortOrder = 1,
            active = true
        )

        // Create first lookup value
        given()
            .contentType(ContentType.JSON)
            .body(originalDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(201)

        // Try to create duplicate
        val duplicateDto = originalDto.copy(displayName = "Duplicate")

        given()
            .contentType(ContentType.JSON)
            .body(duplicateDto)
            .`when`()
            .post(baseUrl)
            .then()
            .statusCode(anyOf(equalTo(400), equalTo(409))) // Bad request or conflict
    }

    private fun createTestLookupValues() {
        val testLookupValues = listOf(
            LookupValue(
                category = "GENDER",
                code = "MALE",
                displayName = "Male",
                sortOrder = 1,
                active = true
            ),
            LookupValue(
                category = "GENDER",
                code = "FEMALE",
                displayName = "Female",
                sortOrder = 2,
                active = true
            ),
            LookupValue(
                category = "BLOOD_GROUP",
                code = "O_POSITIVE",
                displayName = "O+",
                sortOrder = 1,
                active = true
            ),
            LookupValue(
                category = "BLOOD_GROUP",
                code = "A_POSITIVE",
                displayName = "A+",
                sortOrder = 2,
                active = true
            )
        )

        testLookupValues.forEach { lookupRepository.persist(it) }
    }
}
