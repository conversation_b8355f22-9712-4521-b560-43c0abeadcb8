package sirobilt.meghasanjivini.waitingroom.controller

import io.quarkus.test.junit.QuarkusTest
import io.restassured.RestAssured.given
import jakarta.inject.Inject
import org.hamcrest.CoreMatchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestMethodOrder
import org.junit.jupiter.api.MethodOrderer
import org.junit.jupiter.api.Order
import sirobilt.meghasanjivini.masterdata.repository.DoctorRepository
import sirobilt.meghasanjivini.department.repository.ProviderDepartmentMappingRepository
import sirobilt.meghasanjivini.department.repository.DepartmentRepository
import org.acme.appointmentconfig.repositories.AppointmentSlotRepo
import sirobilt.meghasanjivini.masterdata.model.Doctor
import sirobilt.meghasanjivini.department.model.Department
import sirobilt.meghasanjivini.department.model.ProviderDepartmentMapping
import sirobilt.meghasanjivini.appointmentconfig.AppointmentSlot
import sirobilt.meghasanjivini.common.enums.SlotAvailability
import sirobilt.meghasanjivini.department.enums.DepartmentRole
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID

@QuarkusTest
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
class DoctorAvailabilityControllerTest {

    @Inject
    lateinit var doctorRepository: DoctorRepository

    @Inject
    lateinit var departmentRepository: DepartmentRepository

    @Inject
    lateinit var mappingRepository: ProviderDepartmentMappingRepository

    @Inject
    lateinit var slotRepository: AppointmentSlotRepo

    private val baseUrl = "/waiting-room/doctors"

    @BeforeEach
    fun setup() {
        // Clean up test data
        slotRepository.deleteAll()
        mappingRepository.deleteAll()
        doctorRepository.deleteAll()
        departmentRepository.deleteAll()
        
        // Create test data
        createTestData()
    }

    @Test
    @Order(1)
    fun `should get today's doctor availability successfully`() {
        given()
            .`when`()
            .get("$baseUrl/today")
            .then()
            .statusCode(200)
            .body("departments", notNullValue())
            .body("departments.size()", greaterThanOrEqualTo(0))
            .body("totalDoctors", greaterThanOrEqualTo(0))
            .body("availableDoctors", greaterThanOrEqualTo(0))
            .body("busyDoctors", greaterThanOrEqualTo(0))
    }

    @Test
    @Order(2)
    fun `should return structured department data with doctor availability`() {
        given()
            .`when`()
            .get("$baseUrl/today")
            .then()
            .statusCode(200)
            .body("departments", notNullValue())
            .body("departments[0].departmentId", notNullValue())
            .body("departments[0].departmentName", notNullValue())
            .body("departments[0].doctors", notNullValue())
    }

    @Test
    @Order(3)
    fun `should include doctor details in availability response`() {
        given()
            .`when`()
            .get("$baseUrl/today")
            .then()
            .statusCode(200)
            .body("departments[0].doctors[0].doctorId", notNullValue())
            .body("departments[0].doctors[0].doctorName", notNullValue())
            .body("departments[0].doctors[0].specialization", notNullValue())
            .body("departments[0].doctors[0].availableSlots", greaterThanOrEqualTo(0))
            .body("departments[0].doctors[0].totalSlots", greaterThanOrEqualTo(0))
    }

    @Test
    @Order(4)
    fun `should get real-time doctor status successfully`() {
        given()
            .`when`()
            .get("$baseUrl/status")
            .then()
            .statusCode(200)
            .body("timestamp", notNullValue())
            .body("totalDoctors", greaterThanOrEqualTo(0))
            .body("availableDoctors", greaterThanOrEqualTo(0))
            .body("busyDoctors", greaterThanOrEqualTo(0))
            .body("offlineDoctors", greaterThanOrEqualTo(0))
            .body("doctorStatuses", notNullValue())
    }

    @Test
    @Order(5)
    fun `should include individual doctor status in real-time response`() {
        given()
            .`when`()
            .get("$baseUrl/status")
            .then()
            .statusCode(200)
            .body("doctorStatuses", notNullValue())
            .body("doctorStatuses.size()", greaterThanOrEqualTo(0))
    }

    @Test
    @Order(6)
    fun `should handle empty doctor availability gracefully`() {
        // Clear all test data to simulate empty state
        slotRepository.deleteAll()
        mappingRepository.deleteAll()
        doctorRepository.deleteAll()
        departmentRepository.deleteAll()

        given()
            .`when`()
            .get("$baseUrl/today")
            .then()
            .statusCode(200)
            .body("departments", notNullValue())
            .body("departments.size()", equalTo(0))
            .body("totalDoctors", equalTo(0))
            .body("availableDoctors", equalTo(0))
            .body("busyDoctors", equalTo(0))
    }

    @Test
    @Order(7)
    fun `should handle empty doctor status gracefully`() {
        given()
            .`when`()
            .get("$baseUrl/status")
            .then()
            .statusCode(200)
            .body("totalDoctors", equalTo(0))
            .body("availableDoctors", equalTo(0))
            .body("busyDoctors", equalTo(0))
            .body("offlineDoctors", equalTo(0))
            .body("doctorStatuses", notNullValue())
            .body("doctorStatuses.size()", equalTo(0))
    }

    @Test
    @Order(8)
    fun `should return consistent data structure for availability`() {
        given()
            .`when`()
            .get("$baseUrl/today")
            .then()
            .statusCode(200)
            .body("containsKey('departments')", equalTo(true))
            .body("containsKey('totalDoctors')", equalTo(true))
            .body("containsKey('availableDoctors')", equalTo(true))
            .body("containsKey('busyDoctors')", equalTo(true))
            .body("containsKey('timestamp')", equalTo(true))
    }

    @Test
    @Order(9)
    fun `should return consistent data structure for status`() {
        given()
            .`when`()
            .get("$baseUrl/status")
            .then()
            .statusCode(200)
            .body("containsKey('timestamp')", equalTo(true))
            .body("containsKey('totalDoctors')", equalTo(true))
            .body("containsKey('availableDoctors')", equalTo(true))
            .body("containsKey('busyDoctors')", equalTo(true))
            .body("containsKey('offlineDoctors')", equalTo(true))
            .body("containsKey('doctorStatuses')", equalTo(true))
    }

    @Test
    @Order(10)
    fun `should handle concurrent requests gracefully`() {
        // Test multiple concurrent requests
        val responses = (1..5).map {
            given()
                .`when`()
                .get("$baseUrl/today")
                .then()
                .statusCode(200)
                .extract()
                .response()
        }

        // All responses should be successful
        responses.forEach { response ->
            assert(response.statusCode == 200)
            assert(response.jsonPath().get<Any>("departments") != null)
        }
    }

    @Test
    @Order(11)
    fun `should return valid timestamp format`() {
        given()
            .`when`()
            .get("$baseUrl/status")
            .then()
            .statusCode(200)
            .body("timestamp", matchesPattern("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}.*"))
    }

    @Test
    @Order(12)
    fun `should calculate availability metrics correctly`() {
        // Recreate test data for this specific test
        createTestData()

        val response = given()
            .`when`()
            .get("$baseUrl/today")
            .then()
            .statusCode(200)
            .extract()
            .response()

        val totalDoctors = response.jsonPath().getInt("totalDoctors")
        val availableDoctors = response.jsonPath().getInt("availableDoctors")
        val busyDoctors = response.jsonPath().getInt("busyDoctors")

        // Verify that the sum makes sense
        assert(totalDoctors >= 0)
        assert(availableDoctors >= 0)
        assert(busyDoctors >= 0)
        assert(availableDoctors + busyDoctors <= totalDoctors)
    }

    private fun createTestData() {
        // Create test department
        val department = Department().apply {
            departmentId = "DEPT-001"
            facilityId = "1"
            name = "Cardiology"
            code = "CARD"
            description = "Heart and cardiovascular care"
            isActive = true
        }
        departmentRepository.persist(department)

        // Create test doctor
        val doctor = Doctor().apply {
            doctorId = UUID.randomUUID()
            firstName = "Dr. John"
            lastName = "Smith"
            specialization = "Cardiologist"
            isActive = true
        }
        doctorRepository.persist(doctor)

        // Create provider-department mapping
        val mapping = ProviderDepartmentMapping().apply {
            mappingId = "MAPPING-001"
            providerId = doctor.doctorId.toString()
            departmentId = department.departmentId
            facilityId = "1"
            role = DepartmentRole.CONSULTANT
            isPrimary = true
            effectiveFrom = LocalDate.now()
            isActive = true
        }
        mappingRepository.persist(mapping)

        // Create appointment slots
        val slot1 = AppointmentSlot().apply {
            consultantId = doctor.doctorId
            slotDate = LocalDate.now()
            startTime = LocalTime.of(9, 0)
            endTime = LocalTime.of(10, 0)
            slotNumber = 1
            availability = SlotAvailability.AVAILABLE
        }

        val slot2 = AppointmentSlot().apply {
            consultantId = doctor.doctorId
            slotDate = LocalDate.now()
            startTime = LocalTime.of(10, 0)
            endTime = LocalTime.of(11, 0)
            slotNumber = 2
            availability = SlotAvailability.BOOKED
        }

        slotRepository.persist(slot1)
        slotRepository.persist(slot2)
    }
}
